﻿/* Scope all theme variables to Apex pages only */
.apex-layout, .apex-page, .dashboard-container, .claim-overview-container {
    /* Light Mode Colors (Default) */
    --bg-primary: rgba(255, 255, 255, 1);
    --bg-secondary: rgba(248, 250, 252, 1);
    --bg-tertiary: rgba(241, 245, 249, 1);
    --text-primary: rgba(30, 41, 59, 1);
    --text-secondary: rgba(71, 85, 105, 1);
    --text-muted: rgba(100, 116, 139, 1);
    --border-light: rgba(226, 232, 240, 1);
    --icon-background: rgba(96, 165, 250, 0);
    /* My Claims Theme - Blue in Light Mode */
    --primary-color: rgba(44, 90, 160, 1);
    --primary-color-1: rgba(44, 90, 160, 0.1);
    --primary-color-3: rgba(44, 90, 160, 0.3);
    --primary-color-4: rgba(44, 90, 160, 0.4);
    --primary-color-6: rgba(44, 90, 160, 0.6);
    --primary-color-8: rgba(44, 90, 160, 0.8);
    --primary-bg: rgba(44, 90, 160, 0.15);
    --primary-bg2: rgba(44, 90, 160, 0.1);
    --primary-bg3: rgba(44, 90, 160, 0.05);
    /* Search Claims Theme - Orange (same in both modes) */
    --secondary-color: rgba(225, 73, 24, 1);
    --secondary-color-1: rgba(225, 73, 24, 0.1);
    --secondary-color-3: rgba(225, 73, 24, 0.3);
    --secondary-color-4: rgba(225, 73, 24, 0.4);
    --secondary-color-6: rgba(225, 73, 24, 0.6);
    --secondary-color-8: rgba(225, 73, 24, 0.8);
    --secondary-bg: rgba(225, 73, 24, 0.15);
    --secondary-bg2: rgba(225, 73, 24, 0.1);
    --secondary-bg3: rgba(225, 73, 24, 0.05);
    /* Shadows and effects */
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.08);
    /* Status Badge Colors*/
    --bg-badge-neutral: rgba(245, 158, 11, 0.1);
    --text-badge-neutral: rgba(245, 158, 11, 1);
}

/* Dark Mode - Only applies within Apex scoped containers */
@media (prefers-color-scheme: dark) {
    .apex-layout, .apex-page, .dashboard-container, .claim-overview-container {
        /* Dark Mode Colors */
        --bg-primary: rgba(15, 23, 42, 1);
        --bg-secondary: rgba(30, 41, 59, 1);
        --bg-tertiary: rgba(51, 65, 85, 1);
        --text-primary: rgba(248, 250, 252, 1);
        --text-secondary: rgba(226, 232, 240, 1);
        --text-muted: rgba(167, 169, 169, 0.7);
        --border-light: rgba(51, 65, 85, 1);
        --icon-background: rgba(96, 165, 250, 1);
        /* My Claims Theme - Teal in Dark Mode */
        --primary-color: rgba(96, 165, 250, 1);
        --primary-color-1: rgba(96, 165, 250, 0.1);
        --primary-color-3: rgba(96, 165, 250, 0.3);
        --primary-color-4: rgba(96, 165, 250, 0.4);
        --primary-color-6: rgba(96, 165, 250, 0.6);
        --primary-color-8: rgba(96, 165, 250, 0.8);
        --primary-bg: rgba(96, 165, 250, 0.15);
        --primary-bg2: rgba(96, 165, 250, 0.1);
        --primary-bg3: rgba(96, 165, 250, 0.05);
        /* Search Claims Theme remains Orange in dark mode */
        --secondary-color: rgba(230, 129, 97, 1);
        --secondary-color-1: rgba(230, 129, 97, 0.1);
        --secondary-color-3: rgba(230, 129, 97, 0.3);
        --secondary-color-4: rgba(230, 129, 97, 0.4);
        --secondary-color-6: rgba(230, 129, 97, 0.6);
        --secondary-color-8: rgba(230, 129, 97, 0.8);
        --secondary-bg: rgba(230, 129, 97, 0.15);
        --secondary-bg2: rgba(230, 129, 97, 0.1);
        --secondary-bg3: rgba(230, 129, 97, 0.05);
        /* Widget shadows for dark mode */
        --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        --shadow-light: 0 4px 20px rgba(0, 0, 0, 0.3);
        /* Status Badge Colors*/
        --bg-badge-neutral: rgba(255, 193, 7, 0.15);
        --text-badge-neutral: rgba(255, 193, 7, 1);
    }
}