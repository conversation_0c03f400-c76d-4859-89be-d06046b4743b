﻿@inherits LayoutComponentBase
@using Blazored.Toast
@using Blazored.Toast.Configuration

<head>
    <link href="css/ApexTheme.css" rel="stylesheet" />
</head>

<div class="page">
    <div class="content">
        @Body
    </div>

    <BlazoredToasts Position="ToastPosition.TopRight"
                    IconType="IconType.FontAwesome"
                    Timeout="9"
                    SuccessIcon="fas fa-check-circle"
                    ErrorIcon="fas fa-bug"
                    WarningIcon="fas fa-exclamation-triangle"
                    InfoIcon="fas fa-info-circle"
                    SuccessClass="success-toast-override"
                    ErrorClass="error-toast-override"
                    WarningClass="warning-toast-override"
                    InfoClass="info-toast-override"
                    ShowCloseButton="true"
                    ShowProgressBar="true"
                    MaxToastCount="5"
                    RemoveToastsOnNavigation="true" />
</div>
