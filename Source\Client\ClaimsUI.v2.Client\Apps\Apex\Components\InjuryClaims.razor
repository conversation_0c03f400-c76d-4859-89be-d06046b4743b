﻿<div class="widget">
    <div class="widget-header">
        <div class="header-left">
            <span class="widget-icon">🏥</span>
            <h3 class="widget-title">Injury Claims</h3>
        </div>
        @if (!isLoading)
        {
            <div class="header-right">
                @{ int activeCount = injuries?.Count() ?? 0; }
                @if(activeCount > 0)
                {
                    <div class="active-badge">@activeCount ACTIVE</div>
                }
            </div>
        }
    </div>
    <div class="widget-content">
        @if (isLoading)
        {
            <div class="loading-indicator">Loading...</div>
        }
        else if (injuries.Any())
        {
            <div class="injury-table">
                <div class="injury-claims-list">
                    @foreach (InjuryClaimsModel injury in injuries)
                    {
                        <div class="injury-claim-card">
                            <div class="injury-header">
                                <div class="person-indicator">
                                    <span class="person-icon">👤</span>
                                    <span class="person-name">@injury.Name (@injury.Role)</span>
                                </div>
                            </div>
                            <div class="injury-details">
                                @if(!string.IsNullOrWhiteSpace(injury.InjuryDescription))
                                {
                                    <div class="detail-row">
                                        <span class="detail-label">Injury Type:</span>
                                        <span class="detail-value">@injury.InjuryDescription</span>
                                    </div>
                                }
                                @if (!string.IsNullOrWhiteSpace(injury.MedicalRecordsStatus))
                                {
                                    <div class="detail-row">
                                        <span class="detail-label">Medical Records:</span>
                                        <span class="detail-value medical-records">
                                            @if (injury.MedicalRecordsStatus?.ToLower() == "requested")
                                            {
                                                <span class="records">🔍 @injury.MedicalRecordsStatus</span>
                                            }
                                            else
                                            {
                                                <span class="records">✅ @injury.MedicalRecordsStatus</span>
                                            }
                                        </span>
                                    </div>
                                }
                                @if (!string.IsNullOrWhiteSpace(injury.Treatment))
                                {
                                    <div class="detail-row">
                                        <span class="detail-label">Treatment:</span>
                                        <span class="detail-value">@injury.Treatment</span>
                                    </div>
                                }
                                @if (!string.IsNullOrWhiteSpace(injury.TreatmentStatus))
                                {
                                    <div class="detail-row">
                                        <span class="detail-label">Status:</span>
                                        <span class="detail-value">
                                            @if (string.IsNullOrWhiteSpace(injury.Treatment))
                                            {
                                                <span>✅ Closed - No Treatment</span>
                                            }
                                            else if(injury.TreatmentStatus?.ToLower() == "closed")
                                            {
                                                <span>✅ @injury.TreatmentStatus</span>
                                            }
                                            else
                                            {
                                                <span class="status-active">@injury.TreatmentStatus</span>
                                            }
                                        </span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
        else
        {   
            <div class="empty-state">
                <div class="empty-icon">🏥</div>
                <p>No injury claims data available</p>
            </div>
        }
    </div>
</div>