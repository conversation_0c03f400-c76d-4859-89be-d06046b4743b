﻿using Microsoft.AspNetCore.Components;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Components
{
    public partial class ClaimOverview : ComponentBase
    {
        [Inject] private IHttpDispatcher HttpDispatcher { get; set; }
        [Inject] private ITranslatorService Translator { get; set; }

        [Parameter] public string ClaimNumber { get; set; } = string.Empty;
        [Parameter] public ClaimSummaryModel ClaimSummary { get; set; }

        private bool isLoading = false;

        protected override async Task OnInitializedAsync()
        {
            await LoadOverview();
        }

        private async Task LoadOverview()
        {
            if (ClaimSummary != null && !ClaimSummary.IsWidgetLoaded("Exposures"))
            {
                try
                {
                    isLoading = true;
                    StateHasChanged();

                    // Fetch exposures
                    IEnumerable<Exposure> epicExposures = await GetEpicExposuresByClaimNumber(ClaimSummary.ClaimNumber);
                    epicExposures = epicExposures.Where(x => x.ExposureStatus == "Open" || x.ExposureStatus == "Re-Opened");
                    ClaimSummary.Exposures = Translator.TranslateRange<ExposureModel>(epicExposures).ToList();
                    ClaimSummary.MarkWidgetAsLoaded("Exposures");
                }
                catch (Exception ex)
                {
                    // TODO: Add proper logging
                    Console.WriteLine($"Error loading contacts: {ex.Message}");
                }
                finally
                {
                    isLoading = false;
                    StateHasChanged();
                }
            }
        }

        private async Task<IEnumerable<Exposure>> GetEpicExposuresByClaimNumber(string claimNumber)
        {
            return await HttpDispatcher.Get<List<Exposure>>($"{Routes.Exposure.GetExposuresByClaimNumber}{claimNumber}");
        }
    }
}