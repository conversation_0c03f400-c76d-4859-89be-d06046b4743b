﻿<div class="widget">
    <div class="widget-header">
        <div class="header-left">
            <div class="widget-icon">🚗</div>
            <h3 class="widget-title">Vehicle Information</h3>
        </div>
        @if (!isLoading)
        {
            <div class="header-right">
                <div class="vehicle-summary">
                    @if (!ClaimSummary.Vehicles.All(v => v.IsICAAPComplete))
                    {
                        <span class="pending-badge">ICAAP PENDING</span>
                    }
                    else
                    {
                        <span class="complete-badge">ICAAP COMPLETE</span>

                }
                </div>
            </div>
        }
    </div>
    <div class="widget-content">
        @if (isLoading)
        {      
            <div class="loading-indicator">Loading...</div>
        }
        else if (vehicles != null)        
        {
            <div class="vehicle-table">
                <div class="vehicle-list">
                    @foreach (VehicleInformationModel vehicle in vehicles)
                    {
                        <div class="vehicle-card">
                            <div class="vehicle-header">
                                <div class="vehicle-type-indicator">
                                    <span class="vehicle-type-dot @(vehicle.Type?.ToLower() == "insured" ? "insured" : "claimant")"></span>
                                    <span class="vehicle-type-label">@(vehicle.Type ?? "Unknown") Vehicle</span>
                                    <span class="vehicle-title">@vehicle.Description</span>
                                </div>
                            </div>
                            <div class="vehicle-status-row">                            
                                @if (vehicle.IsICAAPComplete)                            
                                {
                                    <span class="status-badge status-complete">iCAAP Run</span>
                                }
                                else
                                {
                                    <span class="status-badge status-warning">iCAAP Pending</span>
                                }
                                @if(vehicle.IsEstimate)
                                {  
                                    <span class="status-badge status-complete">Estimate On Hand</span>
                                }
                                else
                                {
                                    <span class="status-badge status-warning">No Estimate Yet</span>
                                }
                                @if(!string.IsNullOrEmpty(vehicle.SupplementStatus))
                                {
                                    <span class="status-badge @GetSupplementStatusClass(vehicle.SupplementStatus)">
                                        @GetSupplementStatusText(vehicle.SupplementStatus)</span>
                                }
                                @if(vehicle.IsTotalLoss.HasValue)
                                {
                                    @if(vehicle.IsTotalLoss.Value)
                                    {
                                        <span class="status-badge status-warning">Total Loss</span>
                                    }
                                    else
                                    {
                                        <span class="status-badge status-complete">Not Total Loss</span>
                                    }
                                }
                                @* @if(vehicle.Rental != null)
                                {
                                    <span class="status-badge status-complete">Rental</span>
                                }
                                @if(vehicle.Storage != null)
                                {
                                    <span class="status-badge status-pending">Towed/Storage</span>
                                } *@
                            </div>
                            <div class="vehicle-details">
                                @if (vehicle.EstimateAmount.HasValue)
                                {
                                    <div class="detail-item">
                                        <span class="detail-label">Estimate:</span>
                                        <span class="detail-value">
                                            @string.Format(System.Globalization.CultureInfo.GetCultureInfo("en-US"), "{0:C0}", vehicle.EstimateAmount)
                                            <span class="detail-extra">| Deductible: $500</span>
                                        </span>
                                    </div>
                                }
                                @if (vehicle.Location != null)
                                {
                                    <div class="detail-item">
                                        <span class="detail-label">Location:</span>
                                        <span class="detail-value">@vehicle.Location.Name, @vehicle.Location.City @vehicle.Location.State</span>
                                    </div>
                                }
                                @if(vehicle.Rental != null)
                                {
                                    <div class="detail-item">
                                        <span class="detail-label-red">Rental:</span>
                                        <span class="detail-value">@vehicle.Rental.Company (@vehicle.Rental.Duration)</span>
                                    </div>
                                }
                                @if(vehicle.Storage != null)
                                {
                                    <div class="detail-item">
                                        <span class="detail-label-red">Storage:</span>
                                        <span class="detail-value">@vehicle.Storage.Cost/day (@vehicle.Storage.Duration) - Authorization @(vehicle.Storage.Authorization ? "Approved" : "Needed")</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-icon">🚗</div>
                <p>No vehicle information available</p>
            </div>
        }
    </div>
</div>

@code
{
    private string GetSupplementStatusClass(string status)
    {
        return status switch
        {
            "No Supplement" => "status-warning",
            "Supplement Likely" => "status-pending",
            "Supplement" => "status-complete",
            _ => "status-complete"
        };
    }
    
    private string GetSupplementStatusText(string status)
    {
        return status switch
        {
            "No Supplement" => "No Supplement",
            "Supplement Likely" => "Supplement Likely",
            "Has Supplement" => "Has Supplement",
            _  => status
        };
    }
}