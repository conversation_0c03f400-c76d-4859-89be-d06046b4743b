﻿using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.AssignmentModels.Vendor;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.ImpactAssessmentModels;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.WorkFlowModels;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.WorkFlowModels.ExecutedStepsDataModels;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.User;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Pages;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Services.Contracts;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Workflows.Interface;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;
using System.Security.Claims;
using static NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Shared.SystemConstants;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Workflows.ComparisonStep
{
    public class SendCCCAssignmentStepComparison : IWorkflowComparisonStep
    {
        private readonly ILogger<SendCCCAssignmentStepComparison> _logger;
        private readonly IWorkflowStepEvaluator _stepEvaluator;
        private readonly IHttpWrapper _httpWrapper;

        public SendCCCAssignmentStepComparison(ILogger<SendCCCAssignmentStepComparison> logger, IWorkflowStepEvaluator stepEvaluator, IHttpWrapper httpWrapper)
        {
            _logger = logger;
            _stepEvaluator = stepEvaluator;
            _httpWrapper = httpWrapper;
        }

        public string Name => throw new NotImplementedException();
        public string SourceValue => WorkflowConstants.ComparisonStep.SendCCCAssignmentSourceValue;
        public async Task<WorkflowStepResult> Execute(MOIWorkFlowModel step, IImpactAssessmentModelService impactAssessmentModelService)
        {
            try
            {
                string currentUserName =
                    (await _httpWrapper.Get<UserModel>(Routes.User.GetCurrentUser)).Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value ?? string.Empty;
                CCCAssignmentModel request = await PopulateCCCAssignmentRequest(impactAssessmentModelService.ImpactAssessment);
                request.IcaapLoggedInUser = await StepHelper.GetUserId(currentUserName);
                CCCAssignmentModel response = await _httpWrapper.Post(Routes.ICAAP.SendCCCAssignment, request);

                CCCAssignmentRequestResponseModel stepResponse = await GetExecutedData(request);

                if (response is not null)
                {
                    stepResponse.Response = response.Response;
                    impactAssessmentModelService.ImpactAssessment.CurrentMOIModel.ResponseMessage = response.Response;
                }
                return _stepEvaluator.Evaluate(step, response.Response, stepResponse);
            }
            catch (Exception ex)
            {
                WorkflowStepResult result = _stepEvaluator.Evaluate<ExecutedDataBaseModel>(step, false.ToString(), null);
                result.Message = ex.Message;
                _logger.LogError(ex, ex.Message);
                return result;
            }
        }

        private async Task<CCCAssignmentRequestResponseModel> GetExecutedData(CCCAssignmentModel model)
        {
            CCCAssignmentRequestResponseModel stepResponse = new()
            {
                AppraiserName = model.AppraiserFirstName + " " + model.AppraiserLastName,
                AssignmentRecipientCCCId = model.AssignmentRecipientCCCId,
                PhoneNumber = model.AppraiserPhone
            };
            return stepResponse;
        }

        private async Task<CCCAssignmentModel> PopulateCCCAssignmentRequest(ImpactAssessmentModel impactAssessment)
        {
            CCCAssignmentModel assignmentRequest = new()
            {
                ClaimId = impactAssessment.ClaimDetails.ExposureSummary.ClaimId,
                EpicClaimId = impactAssessment.ClaimDetails.ExposureSummary.EpicClaimId,
                ContactId = impactAssessment.ClaimDetails.ExposureSummary.ContactId,
                ExposureId = impactAssessment.ClaimDetails.ExposureSummary.EpicExposureId,
                PolicyPersonId = impactAssessment.ClaimDetails.ExposureSummary.Policy.PolicyPersonId,
                ClaimNumber = impactAssessment.ClaimDetails.ExposureSummary.Claim.ClaimNumber,
                ExposureSequenceNumber = impactAssessment.ClaimDetails.ExposureSummary.ExposureSequenceNumber,
                AssignmentRecipientType = impactAssessment.MOIDetail.VendorAssignmentRecipientTypeCode,
                AssignmentRecipientId = impactAssessment.CurrentMOIModel.AppraiserInfo.AppraiserID,
                AssignmentRecipientCCCId = impactAssessment.CurrentMOIModel.AppraiserInfo.AppraiserCCCID,
                InspectionMethod = impactAssessment.MOIDetail.VendorInspectionCode,
                InspectionType = impactAssessment.MOIDetail.VendorInspectionTypeCode,
                InstructionsToEstimator = impactAssessment.CurrentMOIModel.NotesToVendor,
                ClaimTypeCode = impactAssessment.ClaimDetails.ImpactAssessmentVehicle.VendorClaimTypeCode,
                AdjusterId = impactAssessment.ClaimDetails.ExposureSummary.EpicAssignedEmployeeId,
                AdjusterCCCId = impactAssessment.ClaimDetails.ExposureSummary.EpicAssignedToCCCId,
                AdjusterFirstName = impactAssessment.ClaimDetails.ExposureSummary.EpicAssignedToFirstName,
                AdjusterLastName = impactAssessment.ClaimDetails.ExposureSummary.EpicAssignedToLastName,
                AdjusterPhone = impactAssessment.ClaimDetails.ExposureSummary.EpicAssignedToPhone,
                AdjusterEmail = impactAssessment.ClaimDetails.ExposureSummary.EpicAssignedToEmail,
                AppraiserFirstName = impactAssessment.CurrentMOIModel.AppraiserInfo.FirstName,
                AppraiserLastName = impactAssessment.CurrentMOIModel.AppraiserInfo.LastName,
                AppraiserPhone = impactAssessment.CurrentMOIModel.AppraiserInfo.Phone,
                AppraiserType = impactAssessment.CurrentMOIModel.AppraiserInfo.MethodOfInspectionType,
                VINNumber = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.VIN,
                LicensePlate = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.LicensePlateNumber,
                LicenseState = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.State,
                VehicleYear = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.Year,
                Make = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.Make,
                Model = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.Model,
                VehicleUsage = Convert.ToInt32(impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.Mileage),
                Style = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.Style,
                Color = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.Color,
                VehicleType = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicle.VehicleType,
                PriorDamages = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.PriorDamageDescription,
                DamageDescription = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.DamageDescription,
                IsDrivable = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.IsDrivable,
                DamageExtent = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.DamageExtent,
                VehicleLocationType = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.VehicleLocationType,
                PhysicalLocationAddress1 = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.VehicleLocationAddress.AddressLine1,
                PhysicalLocationAddressName = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.PhysicalLocationAddressName,
                PhysicalLocationCity = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.VehicleLocationAddress.City,
                PhysicalLocationCounty = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.VehicleLocationAddress.County,
                PhysicalLocationStateCode = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.VehicleLocationAddress.State,
                PhysicalLocationZipCode = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.VehicleLocationAddress.ZipCode,
                PhysicalLocationPhone = impactAssessment.ClaimDetails.ExposureSummary.PropertyVehicleClaim.PhysicalLocationPhone,
                PrimaryContactFirstName = impactAssessment.ClaimDetails.ExposureSummary.Policy.InsuredFirstName,
                PrimaryContactLastName = impactAssessment.ClaimDetails.ExposureSummary.Policy.InsuredLastName,
                ClaimantFirstName = impactAssessment.ClaimDetails.ExposureSummary.Contact.FirstName,
                ClaimantLastName = impactAssessment.ClaimDetails.ExposureSummary.Contact.LastName,
                LossDescription = impactAssessment.ClaimDetails.ExposureSummary.Claim.LossDescription,
                LossDateTime = impactAssessment.ClaimDetails.ExposureSummary.Claim.LossDate,
                LossReportDateTime = impactAssessment.ClaimDetails.ExposureSummary.Claim.ReportedDate,
                LossLocationAddress1 = impactAssessment.ClaimDetails.ExposureSummary.Claim.LossLocationAddress1,
                LossLocationCity = impactAssessment.ClaimDetails.ExposureSummary.Claim.LossLocationCity,
                LossLocationCounty = impactAssessment.ClaimDetails.ExposureSummary.Claim.LossLocationCounty,
                LossLocationStateCode = impactAssessment.ClaimDetails.ExposureSummary.Claim.LossLocationState,
                LossLocationZipCode = impactAssessment.ClaimDetails.ExposureSummary.Claim.LossLocationZipcode,
                Deductible = impactAssessment.ClaimDetails.ExposureSummary.DeductiblePerIncident,
                PolicyStateCode = impactAssessment.ClaimDetails.ExposureSummary.Policy.PolicyState,
                PolicyStartDate = impactAssessment.ClaimDetails.ExposureSummary.Policy.EffectiveDate,
                PolicyExpirationDate = impactAssessment.ClaimDetails.ExposureSummary.Policy.ExpirationDate,
                PolicySubType = impactAssessment.ClaimDetails.ExposureSummary.Policy.PolicySubType,
                UnderwritingCompany = impactAssessment.ClaimDetails.ExposureSummary.Policy.MCOName,
                AssignmentType = impactAssessment.ClaimDetails.ExposureSummary.AssignmentType,
                ReserveCategory = impactAssessment.ClaimDetails.ExposureSummary.ReserveCategory,
                PolicyProduct = impactAssessment.ClaimDetails.ExposureSummary.Policy.PolicyProduct,

            };
            await PopulateDamageSelection(impactAssessment, assignmentRequest);
            return assignmentRequest;
        }

        private async Task PopulateDamageSelection(ImpactAssessmentModel impactAssessment, CCCAssignmentModel assignmentRequest)
        {
            foreach (string damage in impactAssessment.ClaimSummary.InitialDamages)
            {
                switch (damage)
                {
                    case DamageArea.Front:
                        assignmentRequest.Dmg1Front = true;
                        break;
                    case DamageArea.LeftFront:
                        assignmentRequest.Dmg1LeftFront = true;
                        break;
                    case DamageArea.LeftSide:
                        assignmentRequest.Dmg1LeftSide = true;
                        break;
                    case DamageArea.RoofTop:
                        assignmentRequest.Dmg1RoofTop = true;
                        break;
                    case DamageArea.LeftRear:
                        assignmentRequest.Dmg1LeftRear = true;
                        break;
                    case DamageArea.Electrical:
                        assignmentRequest.Dmg1Electrical = true;
                        break;
                    case DamageArea.Interior:
                        assignmentRequest.Dmg1Interior = true;
                        break;
                    case DamageArea.Rear:
                        assignmentRequest.Dmg1Rear = true;
                        break;
                    case DamageArea.AwningOnly:
                        assignmentRequest.Dmg1AwningOnly = true;
                        break;
                    case DamageArea.RollOver:
                        assignmentRequest.Dmg1RollOver = true;
                        break;
                    case DamageArea.Hood:
                        assignmentRequest.Dmg1Hood = true;
                        break;
                    case DamageArea.RightFront:
                        assignmentRequest.Dmg1RightFront = true;
                        break;
                    case DamageArea.RightSide:
                        assignmentRequest.Dmg1RightSide = true;
                        break;
                    case DamageArea.RightRear:
                        assignmentRequest.Dmg1RightRear = true;
                        break;
                    case DamageArea.Undercarriage:
                        assignmentRequest.Dmg1Undercarriage = true;
                        break;
                    case DamageArea.Mechanical:
                        assignmentRequest.Dmg1Mechanical = true;
                        break;
                }
            }
            foreach (string damage in impactAssessment.ClaimSummary.RelatedDamages)
            {
                switch (damage)
                {
                    case DamageArea.Front:
                        assignmentRequest.Dmg2Front2 = true;
                        break;
                    case DamageArea.LeftFront:
                        assignmentRequest.Dmg2LeftFront = true;
                        break;
                    case DamageArea.LeftSide:
                        assignmentRequest.Dmg2LeftSide = true;
                        break;
                    case DamageArea.RoofTop:
                        assignmentRequest.Dmg2RoofTop = true;
                        break;
                    case DamageArea.LeftRear:
                        assignmentRequest.Dmg2LeftRear = true;
                        break;
                    case DamageArea.Electrical:
                        assignmentRequest.Dmg2Electrical = true;
                        break;
                    case DamageArea.Interior:
                        assignmentRequest.Dmg2Interior = true;
                        break;
                    case DamageArea.Rear:
                        assignmentRequest.Dmg2Rear2 = true;
                        break;
                    case DamageArea.AwningOnly:
                        assignmentRequest.Dmg2AwningOnly = true;
                        break;
                    case DamageArea.RollOver:
                        assignmentRequest.Dmg2RollOver = true;
                        break;
                    case DamageArea.Hood:
                        assignmentRequest.Dmg2Hood = true;
                        break;
                    case DamageArea.RightFront:
                        assignmentRequest.Dmg2RightFront = true;
                        break;
                    case DamageArea.RightSide:
                        assignmentRequest.Dmg2RightSide = true;
                        break;
                    case DamageArea.RightRear:
                        assignmentRequest.Dmg2RightRear = true;
                        break;
                    case DamageArea.Undercarriage:
                        assignmentRequest.Dmg2Undercarriage = true;
                        break;
                    case DamageArea.Mechanical:
                        assignmentRequest.Dmg2Mechanical = true;
                        break;
                }
            }
        }
    }
}