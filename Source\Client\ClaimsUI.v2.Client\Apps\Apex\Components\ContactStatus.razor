﻿@{
    int pendingCount = contacts?.Count(c => c.Status?.ToLower() != "contacted") ?? 0;
    int totalCount = contacts?.Count() ?? 0;
 }
<div class="widget contact-status-widget">
    <div class="widget-header">
        <div class="header-left">
            <div class="widget-icon">📞</div>
            <h3 class="widget-title">Contacts Required</h3>
        </div>
        @if (!isLoading)
        {
        <div class="header-right">
            <div class="contact-summary">
                @if (pendingCount != 0)
                {
                    <span class="pending-badge">@pendingCount PENDING</span>
                }
                else
                {
                    <span class="contacted-badge">CONTACTED</span>
                }
            </div>
        </div>
        }
    </div>

    <div class="widget-content">
        @if (isLoading)
        {
            <div class="loading-indicator">Loading...</div>
        }
        else if (contacts?.Any() == true)
        {
            <div class="contact-table">
                @foreach (ContactStatusModel contact in contacts)
                {
                    <div class="contact-row">
                        <div class="contact-status">
                            @if (contact.Status?.ToLower() == "contacted")
                            {
                                <div class="contact-bullet contacted">
                                    <span class="checkmark">✓</span>
                                </div>
                            }
                            else
                            {
                                <div class="contact-bullet pending">
                                    @contact.ExposureNumber
                                </div>
                            }
                        </div>

                        <div class="contact-info">
                            <div class="contact-name">@contact.Name</div>
                            <div class="contact-role">@contact.Role</div>
                        </div>

                        <div class="himarley-status">
                            <div class="himarley-row">
                                <span class="himarley-title">💬 Hi Marley</span>
                                <span class="himarley-text <EMAIL>">
                                    @if (contact.HiMarleyStatus == "Opted In")
                                    {
                                        <span class="status-icon">✅</span>
                                    }
                                    else if (contact.HiMarleyStatus == "Not Opted In")
                                    {
                                        <span class="status-icon">❌</span>
                                    }
                                    else
                                    {
                                        <span class="status-icon">⏳</span>
                                    }
                                </span>
                            </div>
                        </div>
                    </div>
                }
            </div>
            <div class="contact-footer">
                <span class="contact-total">
                    Total Contacts: @totalCount | Pending: @pendingCount
                </span>
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-icon">👥</div>
                <p>No contacts available</p>
            </div>
        }
    </div>
</div>