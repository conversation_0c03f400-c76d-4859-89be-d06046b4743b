﻿using System;

namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common
{
    public class PropertyVehicleClaim
    {
        public int PropertyId { get; set; }
        public int ClaimID { get; set; }
        public int PropertyVehicleID { get; set; }
        public string EpicPropertyClaimID { get; set; }
        public string AppraisalResourceDetermination { get; set; }
        public DateTime? CalcTimeStamp1 { get; set; }
        public string DamageDescription { get; set; }
        public string DamageExtent { get; set; }
        public string DamageSeverityLevel { get; set; }
        public string DamagesPrimary { get; set; }
        public string DamagesSecondary { get; set; }
        public bool? DeliveryServiceUse { get; set; }
        #region Primary Damages
        public bool? Dmg1AwningOnly { get; set; }
        public bool? Dmg1Electrical { get; set; }
        public bool? Dmg1Front { get; set; }
        public bool? Dmg1Hood { get; set; }
        public bool? Dmg1Interior { get; set; }
        public bool? Dmg1LeftFender { get; set; }
        public bool? Dmg1LeftFront { get; set; }
        public bool? Dmg1LeftQuarterPiece { get; set; }
        public bool? Dmg1LeftRear { get; set; }
        public bool? Dmg1LeftSide { get; set; }
        public bool? Dmg1Mechanical { get; set; }
        public bool? Dmg1Rear { get; set; }
        public bool? Dmg1RightFender { get; set; }
        public bool? Dmg1RightFront { get; set; }
        public bool? Dmg1RightQuarterPiece { get; set; }
        public bool? Dmg1RightRear { get; set; }
        public bool? Dmg1RightSide { get; set; }
        public bool? Dmg1RollOver { get; set; }
        public bool? Dmg1Top { get; set; }
        public bool? Dmg1Undercarriage { get; set; }
        #endregion
        #region Secondary Damages
        public bool? Dmg2AwningOnly { get; set; }
        public bool? Dmg2Electrical { get; set; }
        public bool? Dmg2Front { get; set; }
        public bool? Dmg2Front2 { get; set; }
        public bool? Dmg2Hood { get; set; }
        public bool? Dmg2Interior { get; set; }
        public bool? Dmg2LeftFront { get; set; }
        public bool? Dmg2LeftRear { get; set; }
        public bool? Dmg2LeftSide { get; set; }
        public bool? Dmg2Mechanical { get; set; }
        public bool? Dmg2Rear { get; set; }
        public bool? Dmg2Rear2 { get; set; }
        public bool? Dmg2RightFront { get; set; }
        public bool? Dmg2RightRear { get; set; }
        public bool? Dmg2RightSide { get; set; }
        public bool? Dmg2RollOver { get; set; }
        public bool? Dmg2Roof { get; set; }
        public bool? Dmg2RoofTop { get; set; }
        public bool? Dmg2Side { get; set; }
        public bool? Dmg2Trunk { get; set; }
        public bool? Dmg2Undercarriage { get; set; }
        #endregion
        public bool? DmgCauseFallObjOther { get; set; }
        public bool? DmgCauseFlood { get; set; }
        public bool? DmgCauseHail { get; set; }
        public bool? DmgCauseVandTheft { get; set; }
        public bool? DmgCauseWind { get; set; }
        public int? DriverContactId { get; set; }
        public decimal EstimateAmount { get; set; }
        public string GlassDamageAreaDescription { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsAirbagDeployed { get; set; }
        public bool? IsDamagesOtherThanWindshield { get; set; }
        public bool? IsDriveable { get; set; }
        public bool? IsEngineRunning { get; set; }
        public bool? IsEstimate { get; set; }
        public bool? IsInteriorDamage { get; set; }
        public bool? IsOnPolicy { get; set; }
        public bool? IsOnPremise { get; set; }
        public bool? IsRecovered { get; set; }
        public bool? IsRental { get; set; }
        public bool? IsRepairable { get; set; }
        public bool? IsTheft { get; set; }
        public bool? IsUninhabitable { get; set; }
        public bool? IsUnlisted { get; set; }
        public bool? IsWindshieldOnlyDamages { get; set; }
        public bool? IsWindshieldTwoPieces { get; set; }
        public bool? KeysAccountedFor { get; set; }
        public int? OwnerContactId { get; set; }
        public string PhysicalInspectionLocationName { get; set; }
        public string PhysicalInspectionLocationPhone { get; set; }
        public bool? PriorDamage { get; set; }
        public string PriorDamageDescription { get; set; }
        public DateTime? RecoveryDate { get; set; }
        public bool? RideShareUse { get; set; }
        public string RvNetworkShop { get; set; }
        public bool? VehicleCanBeMoved { get; set; }
        public string VehicleLocationDescription { get; set; }
        public string VehicleLocationType { get; set; }
        public bool? VehicleLocked { get; set; }
        public char WaterCleanUpComp { get; set; }
        public bool? WindowsRolledUp { get; set; }
    }
}