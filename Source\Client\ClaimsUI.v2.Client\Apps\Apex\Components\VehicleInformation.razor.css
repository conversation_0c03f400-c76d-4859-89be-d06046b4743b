﻿.widget {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
    max-height: 350px;
    display: flex;
    flex-direction: column;
}

    .widget:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

/* Widget Header */
.widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right {
    display: flex;
    align-items: center;
}

.widget-icon {
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
}

.vehicle-summary {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pending-badge {
    background: var(--secondary-bg);
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    padding: 2px 12px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.complete-badge {
    background: var(--primary-bg);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: 2px 12px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Widget Content with Scrolling */
.widget-content {
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Vehicle Table - Scrollable (matches contact-table pattern) */
.vehicle-table {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px;
    gap: 16px;
}

    /* Custom scrollbar styling */
    .vehicle-table::-webkit-scrollbar {
        width: 6px;
    }

    .vehicle-table::-webkit-scrollbar-track {
        background: var(--bg-tertiary);
    }

    .vehicle-table::-webkit-scrollbar-thumb {
        background: var(--primary-bg);
        border-radius: 3px;
    }

        .vehicle-table::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color-3);
        }

/* Vehicle List - Remove scrolling from here */
.vehicle-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Vehicle Card */
.vehicle-card {
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all 200ms ease;
}

    .vehicle-card:hover {
        background: var(--primary-color-1);
        border-color: var(--primary-color-3);
        transform: translateY(-1px);
    }

/* Vehicle Header */
.vehicle-header {
    padding: 4px 16px;
}

.vehicle-type-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.vehicle-type-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

    .vehicle-type-dot.insured {
        background-color: var(--primary-color);
    }

    .vehicle-type-dot.claimant {
        background-color: var(--secondary-color);
    }

.vehicle-type-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: 0.5px;
}

.vehicle-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
    display: block;
}

/* Status Row */
.vehicle-status-row {
    padding: 4px 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

/* Status Badges */
.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

    .status-badge.status-complete {
        background: var(--primary-bg);
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
    }

    .status-badge.status-pending {
        background: var(--bg-badge-neutral);
        color: var(--text-badge-neutral);
        border: 1px solid var(--text-badge-neutral);
    }

    .status-badge.status-warning {
        background: var(--secondary-bg);
        color: var(--secondary-color);
        border: 1px solid var(--secondary-color);
    }

/* Vehicle Details */
.vehicle-details {
    padding: 4px 8px;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    font-size: 12px;
}

    .detail-item:last-child {
        margin-bottom: 0;
    }

.detail-label {
    color: var(--text-primary);
    min-width: 70px;
    flex-shrink: 0;
}

.detail-label-red {
    color: var(--secondary-color);
    min-width: 70px;
    flex-shrink: 0;
}

.detail-value {
    color: var(--text-muted);
    font-weight: 500;
    flex: 1;
}

.detail-extra {
    color: var(--text-muted);
    margin-left: 4px;
}

/* Loading and No Data States */
.loading-indicator {
    text-align: center;
    color: rgba(167, 169, 169, 1);
    font-style: italic;
    padding: 2rem;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
    text-align: center;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vehicle-status-row {
        gap: 4px;
    }

    .status-badge {
        font-size: 10px;
        padding: 2px 6px;
    }

    .detail-item {
        flex-direction: column;
        gap: 2px;
    }

    .detail-label, .detail-label-blue, .detail-label-red {
        min-width: auto;
    }
}
