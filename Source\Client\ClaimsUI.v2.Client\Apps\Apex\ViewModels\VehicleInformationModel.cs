﻿namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels
{
    public class VehicleInformationModel
    {
        public string Description { get; set; }
        public string EpicPropertyId { get; set; }
        public decimal? EstimateAmount { get; set; }
        public bool IsAllICAAPComplete { get; set; }
        public bool IsEstimate { get; set; }
        public bool IsICAAPComplete { get; set; }
        public bool? IsTotalLoss { get; set; }
        public Location Location { get; set; }
        public Rental Rental { get; set; }
        public Storage Storage { get; set; }
        public string SupplementStatus { get; set; }
        public string Type { get; set; }
    }

    public class Location
    {
        public string City { get; set; }
        public string Name { get; set; }
        public string State { get; set; }
        public string Status { get; set; }
    }
    
    public class Rental
    {
        public string Company { get; set; }
        public string Duration { get; set; }
    }
    
    public class Storage
    {
        public bool Authorization { get; set; }
        public string Cost { get; set; }
        public string Duration { get; set; }
    }
}