﻿using NGIC.Claims.Shared.Translator;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.Extensions;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Translators
{
    public class ExposureModel_ContactStatusModel : BaseTranslator<ExposureModel, ContactStatusModel>
    {
        protected override ExposureModel DtoToEntity(ExposureModel entity, ContactStatusModel dto)
        {
            throw new NotImplementedException();
        }

        protected override ContactStatusModel EntityToDto(ContactStatusModel dto, ExposureModel entity)
        {
            ContactModel contact = entity.Contact;
            dto.Name = contact.FirstName != null
                    ? contact.FirstName.ToPascalCase() + " " + contact.LastName.ToPascalCase() : contact.OrganizationName;
            dto.Role = contact?.AccidentRole;
            dto.Status = contact?.LastContacted != null ? "Contacted" : "Pending";
            dto.ExposureNumber = entity.SequenceNumber?.ToString() ?? "?";
            return dto;
        } 
    }
}