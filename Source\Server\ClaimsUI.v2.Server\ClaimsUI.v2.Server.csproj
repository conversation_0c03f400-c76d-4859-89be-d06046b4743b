﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>NGIC.Claims.Web.ClaimsUI.v2.Server</AssemblyName>
    <RootNamespace>NGIC.Claims.Web.ClaimsUI.v2.Server</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.16" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
    <PackageReference Include="Shared.CredentialsManager" Version="2.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Client\ClaimsUI.v2.Client\ClaimsUI.v2.Client.csproj" />
    <ProjectReference Include="..\ClaimsUI.v2.BusinessServices\ClaimsUI.v2.BusinessServices.csproj" />
  </ItemGroup>


</Project>
