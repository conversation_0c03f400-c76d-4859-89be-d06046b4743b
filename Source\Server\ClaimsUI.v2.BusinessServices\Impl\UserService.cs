﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using NGIC.Claims.Common.Logger;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.User;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Contracts;
using NGIC.Claims.Web.ClaimsUI.v2.Data;
using System;
using System.Collections.Generic;
using System.DirectoryServices.AccountManagement;
using System.Linq;
using System.Security.Claims;
using System.Security.Principal;
using System.Threading.Tasks;

namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Impl
{
    public class UserService : IUserService
    {
        private readonly IHttpContextAccessor _httpContext;
        private static readonly double _cookieExpiryHours = 24;

        public UserService(IHttpContextAccessor httpContext)
        {
            _httpContext = httpContext;
        }

        public async Task<UserModel> GetUser(ClaimsPrincipal user)
        {
            UserModel userModel = new UserModel
            {
                Claims = new List<ClaimModel>()
            };

            if (user == null)
            {
                return userModel;
            }

            userModel.Username = user.Identity.Name;
            userModel.IsAuthenticated = user.Identity.IsAuthenticated;

            if (userModel.IsAuthenticated)
            {
                await SignInUser(userModel, user);
            }

            return userModel;
        }

        /// <summary>
        /// Creates a cookie for the user so they can access backend, role restricted code.
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        private async Task SignInUser(UserModel model, ClaimsPrincipal user)
        {
            await AddRolesAndClaims(model, user);

            ClaimsIdentity identity = new ClaimsIdentity(CookieAuthenticationDefaults.AuthenticationScheme);

            identity.AddClaims(model.Claims.Select(claim => new Claim(claim.Type, claim.Value)));

            // The cookieExpiresTime should always be set to a time interval GREATER than the client side user caching
            // time interval. If not done this way, a user could potentially be stuck in a situation where they are
            // authenticated on the frontend, but not the backend.
            DateTime cookieExpiresTime = DateTime.UtcNow.AddHours(_cookieExpiryHours);

            AuthenticationProperties loginSettings = new AuthenticationProperties
            {
                IsPersistent = true,
                AllowRefresh = true,
                ExpiresUtc = cookieExpiresTime
            };

            await _httpContext.HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(identity), loginSettings);
        }

        /// <summary>
        /// Add the user
        /// </summary>
        /// <param name="model"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:Validate platform compatibility", Justification = "Supressing platform compatibility warning because we are Windows only.")]
        private async Task AddRolesAndClaims(UserModel model, ClaimsPrincipal user)
        {
            WindowsPrincipal windowsPrincipal = new WindowsPrincipal((WindowsIdentity)user.Identity);
            string[] nameParts = windowsPrincipal.Identity.Name.Split('\\');
            string userDomain = nameParts[0];
            string userName = nameParts.Length > 1 ? nameParts[1] : nameParts[0];
            using (PrincipalContext principalContext = new PrincipalContext(ContextType.Domain, userDomain))
            {
                UserPrincipal userPrincipal = UserPrincipal.FindByIdentity(principalContext, windowsPrincipal.Identity.Name);
                if (userPrincipal is not null)
                {
                    model.Claims.Add(new ClaimModel { Type = ClaimTypes.GivenName, Value = userPrincipal.GivenName });
                    model.Claims.Add(new ClaimModel { Type = ClaimTypes.Surname, Value = userPrincipal.Surname });
                }
                string email = userPrincipal.GetEmailAddress(userDomain + ".com");
                model.Claims.Add(new ClaimModel { Type = ClaimTypes.Email, Value = email });
                model.Claims.Add(new ClaimModel { Type = ClaimTypes.Name, Value = windowsPrincipal.Identity.Name });
            }
        }
    }
}