﻿using Microsoft.Extensions.Logging;
using Moq;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.AssignmentModels.Vendor;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.Claim;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.Exposure;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.ImpactAssessmentModels;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.MOIModels.Shared;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.Property;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.WorkFlowModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Pages;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Services.Contracts;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Workflows;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Workflows.ComparisonStep;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.ICAAP.Workflows.Interface;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;
using Shouldly;

namespace ClaimsUI.v2.Client.ICAAP.Tests.Workflows.ComparisonStep
{
    public class SendCCCAssignmentStepComparisonShould
    {
        [Fact]
        public void ReturnSendCCCAssignmentAsSourceValue()
        {
            Mock<ILogger<SendCCCAssignmentStepComparison>> logger = new();
            Mock<IWorkflowStepEvaluator> stepEvaluator = new();
            Mock<IHttpWrapper> httpWrapper = new Mock<IHttpWrapper>();
            IWorkflowComparisonStep _comparisonStep = new SendCCCAssignmentStepComparison(logger.Object, stepEvaluator.Object, httpWrapper.Object);

            _comparisonStep.SourceValue.ShouldBe(WorkflowConstants.ComparisonStep.SendCCCAssignmentSourceValue);
        }

        [Theory]
        [InlineData("True", 3, false)]
        [InlineData("False", 3, false)]
        public async Task ReturnEvaluationOfSendCCCAssignmentStep(string SendCCCAssignmentResult, int expectedStepID, bool expectedIsNextStepTrue)
        {
            Mock<ILogger<SendCCCAssignmentStepComparison>> logger = new();
            IWorkflowStepEvaluator stepEvaluator = new WorkflowStepEvaluator();
            Mock<IHttpWrapper> httpWrapper = new Mock<IHttpWrapper>();
            Mock<IImpactAssessmentModelService> impactAssessmentModelService = new();

            impactAssessmentModelService.SetupGet(x => x.ImpactAssessment)
                .Returns(new ImpactAssessmentModel()
                {
                    ClaimDetails = new ClaimDetailsModel()
                    {
                        ExposureSummary = new ExposureSummaryModel()
                        {
                            DeductiblePerIncident = "123",
                            PropertyVehicle = new PropertyVehicleModel()
                            {
                                Mileage = "123"
                            },
                        }
                    },
                    MOIModels = new List<BaseMOIModel>()
                    {
                        new BaseMOIModel()
                        {
                            MOIDetails = new MOIDetailModel()
                            {

                            }
                        }
                    }
                }).Verifiable();

            httpWrapper.Setup(x => x.Post(It.IsAny<string>(), It.IsAny<CCCAssignmentModel>())).ReturnsAsync(
            new CCCAssignmentModel()
            {
                Response = SendCCCAssignmentResult
            });


            IWorkflowComparisonStep _comparisonStep = new SendCCCAssignmentStepComparison(logger.Object, stepEvaluator, httpWrapper.Object);

            MOIWorkFlowModel step = new MOIWorkFlowModel()
            {
                SourceValue = "sourceValue",
                ComparisonValue = "comparisonValue",
                MethodOfInspectionEvaluationType = new MOIEvaluationTypeModel()
                {
                    MethodOfInspectionEvaluationType = "bool"
                },
                IsFalseMethodOfInspectionTypeID = 3,
                IsFalseMethodOfInspectionWorkFlowID = 3,
                IsTrueMethodOfInspectionTypeID = 2,
                IsTrueMethodOfInspectionWorkFlowID = 2
            };

            WorkflowStepResult result = await _comparisonStep.Execute(step, impactAssessmentModelService.Object);
            result.NextStepId.ShouldBe(expectedStepID);
            result.IsNextStepTrue.ShouldBe(expectedIsNextStepTrue);

            Mock.VerifyAll();
        }
    }
}