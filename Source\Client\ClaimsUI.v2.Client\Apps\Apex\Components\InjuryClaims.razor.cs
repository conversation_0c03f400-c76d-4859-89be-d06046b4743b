﻿using Blazorise;
using Microsoft.AspNetCore.Components;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Components
{
    public partial class InjuryClaims : ComponentBase
    {
        [Inject] private IHttpDispatcher HttpDispatcher { get; set; }
        [Inject] private ITranslatorService Translator { get; set; }

        [Parameter] public ClaimSummaryModel ClaimSummary { get; set; }

        private List<InjuryClaimsModel> injuries => ClaimSummary?.InjuryClaims;
        public bool isLoading = false;


        protected override async Task OnInitializedAsync()
        {
            if (!ClaimSummary.IsWidgetLoaded("InjuryClaims"))
            {
                await LoadInjuryClaimsData();
            }
        }

        private async Task LoadInjuryClaimsData()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                // Translate ExposureModel to List<InjuryClaimModel> where Coverage contains "Injury"
                ClaimSummary.InjuryClaims = Translator.TranslateRange<InjuryClaimsModel>(ClaimSummary.Exposures)
                    .Where(exp => exp.Coverage.ToLower().Contains("injury"))
                    .ToList();

                // TODO: Get rid of this if statement
                if (!ClaimSummary.InjuryClaims.Any())
                {
                    await Task.Delay(500); // Simulate API delay
                    ClaimSummary.InjuryClaims = GenerateInjuries(new Random().Next(1, 5));
                }
                else
                {
                    // Fill in the rest of the params
                    foreach (InjuryClaimsModel injury in ClaimSummary.InjuryClaims)
                    {

                        injury.MedicalRecordsStatus = GenerateMedicalRecordStatus();
                        injury.Treatment = GenerateTreatment();
                        injury.TreatmentStatus = GenerateTreatmentStatus();
                    }
                }
                ClaimSummary.MarkWidgetAsLoaded("InjuryClaims");
            }
            catch (Exception ex)
            {
                // TODO: Add proper logging
                Console.WriteLine($"Error loading injury claims data: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private async Task<IEnumerable<Exposure>> GetEpicExposuresByClaimNumber(string claimNumber)
        {
            return await HttpDispatcher.Get<List<Exposure>>($"{Routes.Exposure.GetExposuresByClaimNumber}{claimNumber}");
        }

        private static List<InjuryClaimsModel> GenerateInjuries(int contacts)
        {
            List<InjuryClaimsModel> injuries = new();
            for (int i = 0; i < contacts; i++)
            {
                injuries.Add(new InjuryClaimsModel
                {
                    InjuryDescription = GenerateInjuryDescription(),
                    MedicalRecordsStatus = GenerateMedicalRecordStatus(),
                    Name = GenerateName(),
                    Role = GenerateRole(),
                    Treatment = GenerateTreatment(),
                    TreatmentStatus = GenerateTreatmentStatus()
                });
            }
            return injuries;
        }

        private static string GenerateInjuryDescription()
        {
            Random random = new Random();
            string[] injuries = { 
                "Headache, or migraine, right shoulder pain, neck pain",
                "Neck, and back pain, tender and swollen",
                "Total body pain and stiffness, chest and neck pain left side in more pain than right livers and shoulder, swollen neck, tender, and swollen",
                "Shoulder/back pain",
                "Back/side pain",
                "Arm Injury",
                "Neck pain",
                "Lower back",
                "Laceration on bottom lip, Cut & Bruise on Right Inner Knee, whip lash, headaches",
                "Chest pain and abdominal pain",
                "Total body pain and neck, back, headaches wrist, hand swelling", null 
            };
            return injuries[random.Next(injuries.Length)];
        }

        private static string GenerateMedicalRecordStatus()
        {
            Random random = new Random();
            string[] roles = { "Requested", "Received", null };
            return roles[random.Next(roles.Length)];
        }

        private static string GenerateName()
        {
            Random random = new Random();

            string firstName = FirstNames[random.Next(FirstNames.Length)];
            string lastName = LastNames[random.Next(LastNames.Length)];

            return $"{firstName} {lastName}";
        }

        private static readonly string[] FirstNames = {
            "James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda",
            "William", "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica",
            "Thomas", "Sarah", "Christopher", "Karen", "Charles", "Nancy", "Daniel", "Lisa",
            "Matthew", "Betty", "Anthony", "Helen", "Mark", "Sandra", "Donald", "Donna",
            "Steven", "Carol", "Paul", "Ruth", "Andrew", "Sharon", "Joshua", "Michelle",
            "Kenneth", "Laura", "Kevin", "Sarah", "Brian", "Kimberly", "George", "Deborah",
            "Timothy", "Dorothy", "Ronald", "Amy", "Jason", "Angela", "Edward", "Ashley",
            "Jeffrey", "Brenda", "Ryan", "Emma", "Jacob", "Olivia", "Gary", "Cynthia"
        };

        private static readonly string[] LastNames = {
            "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
            "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas",
            "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White",
            "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker", "Young",
            "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
            "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell",
            "Carter", "Roberts", "Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker",
            "Cruz", "Edwards", "Collins", "Reyes", "Stewart", "Morris", "Morales", "Murphy"
        };

        private static string GenerateRole()
        {
            Random random = new Random();
            string[] roles = { 
                "Driver", "Driver & Owner", "Excluded Driver", "Other", "Passenger", "Pedestrian",
                "Undeclared Driver", "Unlisted Driver" };
            return roles[random.Next(roles.Length)];
        }

        private static string GenerateTreatment()
        {
            Random random = new Random();
            string[] treatments = { 
                "Chiropractic", "General Practice", "In-Patient Hospital", "Emergency Treatment",
                "Orthopedic Surgery", "Hospital", "Acupuncture", "Pain Mgmt.-Anesthesiologist", "Psychiatry",
                "Physical Therapy", "Home Health Aid", null
            };
            return treatments[random.Next(treatments.Length)];
        }

        private static string GenerateTreatmentStatus()
        {
            Random random = new Random();
            string[] statuses = { "Open", "Closed" };
            return statuses[random.Next(statuses.Length)];
        }
    }
}