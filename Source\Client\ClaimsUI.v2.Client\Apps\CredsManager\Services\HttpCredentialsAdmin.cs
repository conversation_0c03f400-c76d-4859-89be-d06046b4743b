﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Shared.CredentialsManager.Entities;
using Shared.CredentialsManager.Manager.Contracts;
using Shared.CredentialsManager.Manager.Models;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.CredsManager.Services
{
    public class HttpCredentialsAdmin : ICredentialsAdmin
    {
        private readonly HttpClient _httpClient;
        private const string BaseUrl = "api/Admin";

        public HttpCredentialsAdmin(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        #region ApplicationKey Management

        public async Task<ApplicationKey> CreateApplicationKey(ApplicationKeyCreateModel applicationKeyCreateModel)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{BaseUrl}/CreateApplicationKey", applicationKeyCreateModel);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<ApplicationKey>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create application key: {ex.Message}", ex);
            }
        }

        public async Task<ApplicationKey> UpdateApplicationKey(ApplicationKeyUpdateModel applicationKeyUpdateModel)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{BaseUrl}/UpdateApplicationKey", applicationKeyUpdateModel);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<ApplicationKey>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to update application key: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<ApplicationKey>> GetAllApplicationKeys()
        {
            try
            {
                var response = await _httpClient.GetAsync($"{BaseUrl}/GetAllApplicationKeys");

                if (!response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    throw new Exception($"API returned {response.StatusCode}: {content}");
                }

                var contentType = response.Content.Headers.ContentType?.MediaType;
                if (contentType != "application/json")
                {
                    var content = await response.Content.ReadAsStringAsync();
                    throw new Exception($"API returned {contentType} instead of JSON. First 500 chars: {content.Substring(0, Math.Min(500, content.Length))}");
                }

                var result = await response.Content.ReadFromJsonAsync<IEnumerable<ApplicationKey>>();
                return result ?? new List<ApplicationKey>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to get application keys: {ex.Message}", ex);
            }
        }

        public async Task<ApplicationKey> GetApplicationKeyByName(string applicationName)
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<ApplicationKey>($"{BaseUrl}/GetApplicationKeyByName?applicationName={Uri.EscapeDataString(applicationName)}");
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to get application key: {ex.Message}", ex);
            }
        }

        #endregion

        #region ApplicationSecret Management

        public async Task<ApplicationSecret> CreateApplicationSecret(ApplicationSecretModel secretModel)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{BaseUrl}/CreateApplicationSecret", secretModel);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<ApplicationSecret>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create application secret: {ex.Message}", ex);
            }
        }

        public async Task<ApplicationSecret> UpdateApplicationSecret(ApplicationSecretModel secretModel)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{BaseUrl}/UpdateApplicationSecret", secretModel);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<ApplicationSecret>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to update application secret: {ex.Message}", ex);
            }
        }

        public async Task<IEnumerable<ApplicationSecret>> GetAllApplicationSecretsByApplicationName(string applicationName)
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<IEnumerable<ApplicationSecret>>($"{BaseUrl}/GetAllApplicationSecretsByApplicationName?applicationName={Uri.EscapeDataString(applicationName)}");
                return response ?? new List<ApplicationSecret>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to get application secrets: {ex.Message}", ex);
            }
        }

        public async Task<ApplicationSecret> GetApplicationSecretByNameAndApplication(string applicationName, string secretName)
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<ApplicationSecret>($"{BaseUrl}/GetApplicationSecretByNameAndApplication?applicationName={Uri.EscapeDataString(applicationName)}&secretName={Uri.EscapeDataString(secretName)}");
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to get application secret: {ex.Message}", ex);
            }
        }

        public string DecryptSecretValue(ApplicationSecret secret)
        {
            // This needs to be async, so we'll handle it differently
            var task = DecryptSecretValueAsync(secret);
            return task.GetAwaiter().GetResult();
        }

        private async Task<string> DecryptSecretValueAsync(ApplicationSecret secret)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{BaseUrl}/DecryptSecretValue", secret);
                response.EnsureSuccessStatusCode();
                var result = await response.Content.ReadFromJsonAsync<DecryptResponse>();
                return result?.Value ?? string.Empty;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to decrypt secret value: {ex.Message}", ex);
            }
        }

        #endregion

        #region System Management

        public string GenerateLockBoxMasterKey()
        {
            throw new NotImplementedException("This method should be called on the server side only");
        }

        #endregion

        private class DecryptResponse
        {
            public string Value { get; set; }
        }
    }
}