﻿<div class="widget">
    <div class="widget-header">
        <div class="header-left">
            <span class="widget-icon">⚖️</span>
            <h3 class="widget-title">Liability</h3>
        </div>
        @if (!isLoading)
        {
            <div class="header-right">
                <div class="liability-summary">
                    @if (liability?.Status?.ToLower() == "pending")
                    {
                        <span class="pending-badge">@(liability?.Status)</span>
                    }
                    else
                    {
                        <span class="determined-badge">@(liability?.Status)</span>
                    }
                </div>
            </div>
        }
    </div>
    <div class="widget-content">
        @if (isLoading)
        {
            <div class="loading-indicator">Loading...</div>
        }
        else if (liability != null)
        {
            <div class="liability-content">
                <!-- Investigation Section -->
                <div class="liability-section">
                    <div class="section-row">
                        <span class="section-label">At Fault:</span>
                        @if (string.IsNullOrWhiteSpace(liability.AtFaultPercentage))
                        {
                            <span class="section-value">Not Determined</span>
                        }
                        else
                        {
                            <span class="section-value">@liability.AtFaultPercentage</span>
                        }
                    </div>
                </div>

                <!-- Police Report Section -->
                <div class="liability-section">
                    <div class="section-row">
                        <span class="section-label">Police Report:</span>
                        <span class="section-value police-report">
                            @if (liability.PoliceReportStatus?.ToLower() == "requested")
                            {
                                <span class="status-icon">🔍</span>
                                <span>Requested</span>
                            }
                            else if (liability.PoliceReportStatus?.ToLower() == "received")
                            {
                                <span class="status-icon">✅</span>
                                <span>Received</span>
                            }
                            else
                            {
                                <span class="status-icon">❌</span>
                                <span>@(liability.PoliceReportStatus ?? "Not Available")</span>
                            }
                        </span>
                    </div>
                </div>

                <!-- Statements Section -->
                <div class="liability-section">
                    <div class="section-row">
                        <span class="section-label">Statements:</span>
                        <span class="section-value">@liability.StatementsCompleted of @liability.StatementsTotal Complete</span>
                    </div>
                </div>

                <!-- SIU Red Flags Section -->
                @if (liability.SIURedFlags?.Any() == true)
                {
                    <div class="liability-section siu-section">
                        <div class="siu-header">
                            <span class="siu-icon">⚠️</span>
                            <span class="siu-title">SIU Red Flags</span>
                        </div>
                        <div class="siu-flags">
                            @foreach (string flag in liability.SIURedFlags)
                            {
                                <div class="siu-flag">
                                    <span class="flag-bullet">•</span>
                                    <span class="flag-text">@flag</span>
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-icon">⚖️</div>
                <p>No liability claims available</p>
            </div>
        }
    </div>
</div>