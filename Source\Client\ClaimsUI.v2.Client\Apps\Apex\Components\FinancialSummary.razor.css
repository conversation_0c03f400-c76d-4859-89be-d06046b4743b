﻿.widget {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
}

    .widget:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

/* Widget Header */
.widget-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
}

.widget-icon {
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
}

/* Widget Content */
.widget-content {
    padding: 16px;
}

/* Items */
.financial-info {
    flex: 1;
    min-width: 0;
}

.financial-name {
    font-size: 12px;
    font-weight: 550;
    margin-bottom: 8px;
}

.financial-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.financial-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 12px;
    text-align: center;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

    .financial-item:hover {
        background: var(--primary-color-1);
        border-color: var(--primary-color-3);
        transform: translateY(-1px);
    }

.financial-label {
    font-size: 11px;
    font-weight: 500;
    color: var(--text-primary);
}

.financial-amount {
    font-size: 16px;
    font-weight: 550;
    color: var(--primary-color);
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
    text-align: center;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
}

/* Loading and No Data States */
.loading-indicator {
    text-align: center;
    color: rgba(167, 169, 169, 1);
    font-style: italic;
    padding: 2rem;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .financial-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .financial-item {
        padding: 0.75rem 0.5rem;
    }

    .financial-amount {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .financial-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}