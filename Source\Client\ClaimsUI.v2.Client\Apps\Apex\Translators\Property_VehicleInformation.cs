﻿using NGIC.Claims.Shared.Translator;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Translators
{
    public class Property_VehicleInformationModel : BaseTranslator<Property, VehicleInformationModel>
    {
        protected override Property DtoToEntity(Property entity, VehicleInformationModel dto)
        {
            throw new NotImplementedException();
        }

        protected override VehicleInformationModel EntityToDto(VehicleInformationModel dto, Property entity)
        {
            PropertyVehicle pv = entity.PropertyVehicle;
            PropertyVehicleClaim pvc = entity.PropertyVehicleClaim;

            dto.Description = (pv.Year + " " + pv.Make + " " + pv.Model).Trim();
            dto.EpicPropertyId = entity.EpicPropertyId;
            dto.EstimateAmount = pvc.EstimateAmount;
            dto.IsEstimate = pvc.IsEstimate ?? false;
            //dto.Location = 
            //dto.Rental =
            //dto.Storage =
            //dto.SupplementStatus =
            dto.Type = entity.PropertyType;

            return dto;
        }
    }
}