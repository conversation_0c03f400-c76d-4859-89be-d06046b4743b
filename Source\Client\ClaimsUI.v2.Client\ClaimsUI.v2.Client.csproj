﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>NGIC.Claims.Web.ClaimsUI.v2.Client</AssemblyName>
    <RootNamespace>NGIC.Claims.Web.ClaimsUI.v2.Client</RootNamespace>
    <ImplicitUsings>enable</ImplicitUsings>
    <PublishTrimmed>False</PublishTrimmed>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Apps\ICAAP\ViewModels\**" />
    <Compile Remove="Extensions\**" />
    <Content Remove="Apps\ICAAP\ViewModels\**" />
    <Content Remove="Extensions\**" />
    <EmbeddedResource Remove="Apps\ICAAP\ViewModels\**" />
    <EmbeddedResource Remove="Extensions\**" />
    <None Remove="Apps\ICAAP\ViewModels\**" />
    <None Remove="Extensions\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.Modal" Version="6.0.1" />
    <PackageReference Include="Blazored.SessionStorage" Version="2.4.0" />
    <PackageReference Include="Blazored.Toast" Version="4.2.1" />
    <PackageReference Include="Blazorise.Animate" Version="1.8.0" />
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.8.0" />
    <PackageReference Include="Blazorise.DataGrid" Version="1.8.0" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.8.0" />
    <PackageReference Include="Blazorise.LoadingIndicator" Version="1.8.0" />
    <PackageReference Include="Blazorise.SpinKit" Version="1.8.0" />
    <PackageReference Include="Common.Status" Version="3.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.17" PrivateAssets="all" />
    <PackageReference Include="MudBlazor" Version="8.9.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Shared.Translator" Version="2.2.2" />
    <PackageReference Include="Shared.CredentialsManager" Version="2.0.2" />
    <PackageReference Include="System.Net.Http.Json" Version="8.0.1" />
    <PackageReference Include="System.Text.Encodings.Web" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\css\NGIC\" />
    <Folder Include="wwwroot\css\webfonts\" />
  </ItemGroup>

  <ItemGroup>
    <None Include="wwwroot\css\ApexTheme.css" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\ClaimsUI.v2.BusinessModels\ClaimsUI.v2.BusinessModels.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\icon-192.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\images\ICAAP\Auto.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\images\check-circle.svg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\images\directions.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\images\ICAAP\rv_bw.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\custom-popover.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\interopFunctions.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\js\scroll-to.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <TrimmerRootDescriptor Include="keyvaluepair-linker.xml" />
  </ItemGroup>

</Project>
