﻿@page "/apex/claimdashboard"
@layout ApexLayout

<PageTitle>Claims Adjuster Dashboard</PageTitle>

<div class="dashboard-container">
    <!-- Header -->
    <header class="dashboard-header">
        <h1 class="dashboard-title">Claims Adjuster Dashboard</h1>
        <div class="user-info">
            Welcome, @displayName (Adjuster ID: @employeeNumber)
        </div>
    </header>

    <!-- Navigation -->
    <nav class="dashboard-nav">
        <div class="nav-item @(activeTab == "dashboard" ? "active" : "")" @onclick='() => SetActiveTab("dashboard")'>
            Dashboard
        </div>
        @foreach (string tab in openTabs)
        {
            <div class="nav-item @(activeTab == tab ? "active" : "") @(GetTabColorClass(tab))" @onclick ='() => SetActiveTab(tab)'>
                @tab
                <button class="close-tab-btn" @onclick="(e) => CloseTab(e, tab)" @onclick:stopPropagation="true">×</button>
            </div>
        }
    </nav>

    @if(activeTab == "dashboard")
    {
        <!-- Two Column Layout -->
        <div class="dashboard-content">
            <!-- Left Column - My Claims -->
            <div class="left-column">
                <div class="section-header">
                    <h2 class="section-title"><span class="tab-icon">👤</span>My Claims</h2>
                </div>
                <div class="filter-section">
                    <input type="text" @bind="filterTerm" @oninput="ApplyFilters"
                    class="filter-input"
                    placeholder="Filter by claim number, policy number, or insured name..." />
                        <button class="refresh-filter-btn" @onclick="RefreshMyClaims">
                            <span>Refresh</span>
                        </button>
                    @if(!string.IsNullOrEmpty(filterTerm))
                    {
                        <button class="clear-filter-btn" @onclick="ClearFilters">Clear Filters</button>
                    }

                </div>
                <!-- Claims Table -->
                <div class="table-container">
                    <table class="claims-table">
                        <thead>
                            <tr>
                                <th>Claim</th>
                                <th>Policy</th>
                                <th>Insured</th>
                                <th>Loss Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                        @if(isLoadingMyClaims)
                        {
                            <tr>
                                <td colspan="5" class="loading-cell">
                                    <div class="loading-indicator">Loading...</div>
                                </td>
                            </tr>
                        }
                        else if (displayedClaims.Any())
                        {
                            @foreach (ClaimSummaryModel claim in displayedClaims)
                            {
                                @if (claim.ClaimNumber != null)
                                {
                                    <tr class="claim-row" @onclick="() => SelectClaim(claim)">
                                        <td>@claim.ClaimNumber</td>
                                        <td>@claim.PolicyNumber</td>
                                        <td>@claim.InsuredName</td>
                                        <td>@claim.IncidentDate?.ToString("MMM dd, yyyy")</td>
                                        <td>
                                            <span class="status-badge <EMAIL>().Replace(" ", "-")">
                                                @claim.Status.ToUpper()
                                            </span>
                                        </td>
                                    </tr>
                                }
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="5" class="no-results">
                                    <span>No claims match your filter criteria</span>
                                </td>
                            </tr>
                        }
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- Right Column - My Claims -->
            <div class="right-column">
                <div class="section-header">
                    <h2 class="section-title">
                        <span class ="tab-icon">🔍</span>Search Claims
                    </h2>
                </div>
                <div class="search-section">
                    <div class="search-input-group">
                        <input type="text" @bind="searchTerm" @onkeyup="HandleKeyDown"
                            class="search-input"
                            placeholder="Search claim number..." />
                        <button class="search-button" @onclick="SearchClaims">
                            <span>Search</span>
                        </button>
                        @if (searchResults.Any())
                        {
                            <button class="clear-search-btn"@onclick="ClearSearch">Clear Results</button>
                        }
                    </div>
                </div>
                <!-- Claims Table -->
                <div class="table-container">
                    <table class="claims-table">
                        <thead>
                            <tr>
                                <th>Claim</th>
                                <th>Policy</th>
                                <th>Insured</th>
                                <th>Loss Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                        @if (isLoadingSearch)
                        {
                            <tr>
                                <td colspan="5" class="loading-cell">
                                    <div class="loading-indicator">Loading...</div>
                                </td>
                            </tr>
                        }
                        else if (searchResults.Any())
                        {
                            @foreach (ClaimSummaryModel claim in searchResults)
                            {
                                @if (claim.ClaimNumber != null)
                                {
                                    <tr class="claim-row" @onclick="() => SelectClaim(claim)">
                                        <td>@claim.ClaimNumber</td>
                                        <td>@claim.PolicyNumber</td>
                                        <td>@claim.InsuredName</td>
                                        <td>@claim.IncidentDate?.ToString("MMM dd, yyyy")</td>
                                        <td>
                                            <span class="status-badge <EMAIL>?.ToLower().Replace(" ", "-")">
                                                @claim.Status?.ToUpper()
                                            </span>
                                        </td>
                                    </tr>
                                }
                            }
                        }
                        else     
                        {                               
                            <tr>                           
                                <td colspan="5" class="no-results">
                                @if (string.IsNullOrEmpty(searchTerm))
                                {
                                    <div class ="empty-search-state">
                                        <div class="empty-icon">🔍</div>
                                        <div class="empty-title">Search All Claims</div>
                                        <div class="empty-description">
                                            Enter a claim number above to search across all systems and find claims that aren't in your portfolio.
                                        </div>
                                    </div>
                                }
                                else
                                {       
                                    <span>No results found</span>
                                }   
                                </td>
                            </tr>
                        }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Claim Detail View -->
        <!-- Claim Header -->
        ClaimSummaryModel claimSummary = GetClaimSummaryForTab(activeTab);
        <div class="claim-header @(claimSummary?.IsMyClaim == true ? "my-claim-header" : "search-claim-header" )">
            <div class="claim-header-main">
                <h1 class="claim-number">Claim: @activeTab</h1>
                <div class="claim-details">
                    <span class="claim-detail-item">
                        <span class="detail-label">Loss:</span>
                        <span class="detail-value">@claimSummary?.IncidentDate?.ToString("MMM dd, yyyy")</span>
                    </span>
                    <span class="detail-separator">•</span>
                    <span class="claim-detail-item">
                        <span class="detail-label">Insured:</span>
                        <span class="detail-value">@claimSummary?.InsuredName</span>
                    </span>
                    <span class="detail-separator">•</span>
                    <span class="claim-detail-item">
                        <span class="detail-label">Policy:</span>
                        <span class="detail-value">@claimSummary?.PolicyNumber</span>
                    </span>
                </div>
            </div>
        </div>

        <div>            
            <ClaimOverview ClaimNumber="@activeTab" ClaimSummary=claimSummary />
        </div>
    }
</div>