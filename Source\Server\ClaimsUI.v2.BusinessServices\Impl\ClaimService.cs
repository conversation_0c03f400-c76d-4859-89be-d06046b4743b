﻿using NGIC.Claims.Common.Status.Response.Contracts;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Contracts;
using NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Contracts;
using System.Threading.Tasks;
using Proxy = NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Proxies.FunctionalCore;

namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Impl
{
    public class ClaimService : BaseService, IClaimService
    {
        public ClaimService(
            IDispatcher dispatcher,
            ITranslatorService translator,
            IStatusResponse statusResponse
        ) : base(translator, dispatcher, statusResponse)
        {
        }
       
        public async Task<Claim> GetClaimByClaimNumber(string claimNumber)
        {
            Claim claim = new();
            Proxy.Claim apexClaim = await Dispatcher.DispatchRequest<Proxy.Claim, Proxy.IFunctionalCoreProxy>(
                                            x => x.Claim_GetApexClaimByClaimNumberAsync(claimNumber));
            if (apexClaim != null)
            {
                claim = Translator.TranslateDefault<Claim>(apexClaim);
            }
            return claim;
        }
    }
}