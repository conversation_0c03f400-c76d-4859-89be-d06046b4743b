﻿using NGIC.Claims.Shared.Translator;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.Extensions;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Translators
{
    public class ExposureModel_InjuryClaimsModel : BaseTranslator<ExposureModel, InjuryClaimsModel>
    {
        protected override ExposureModel DtoToEntity(ExposureModel entity, InjuryClaimsModel dto)
        {
            throw new NotImplementedException();
        }

        protected override InjuryClaimsModel EntityToDto(InjuryClaimsModel dto, ExposureModel entity)
        {
            ContactModel contact = entity.Contact;
            dto.Coverage = entity.Coverage.CodeDescription;
            dto.InjuryDescription = contact?.InjuryDescription;
            //dto.MedicalRecordsStatus = 
            dto.Name = contact.FirstName != null
                    ? contact.FirstName.ToPascalCase() + " " + contact.LastName.ToPascalCase() : contact.OrganizationName;
            dto.Role = contact?.AccidentRole;
            //dto.Treatment = 
            //dto.TreatmentStatus =
            return dto;
        }
    }
}