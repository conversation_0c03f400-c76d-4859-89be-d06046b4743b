﻿<div class="widget">
    <div class="widget-header">
        <span class="widget-icon">💰</span>
        <h3 class="widget-title">Financial Summary</h3>
    </div>
    <div class="widget-content">
        @if (isLoading)
        {
            <div class="loading-indicator">Loading...</div>
        }
        else if (financialSummary != null)
        {
            <div class="financial-grid">
                <div class="financial-item">
                    <div class="financial-label">Reserve</div>
                    <div class="financial-amount">@financialSummary.Reserve.ToString("C")</div>
                </div>
                <div class="financial-item">
                    <div class="financial-label">Paid</div>
                    <div class="financial-amount">@financialSummary.Paid.ToString("C")</div>
                </div>
                @if (financialSummary.Outstanding > 0)
                {
                    <div class="financial-item">
                        <div class="financial-label">Outstanding</div>
                        <div class="financial-amount">@financialSummary.Outstanding.ToString("C")</div>
                    </div>
                }
                <div class="financial-item">
                    <div class="financial-label">Deductible</div>
                    <div class="financial-amount">@financialSummary.Deductible.ToString("C")</div>
                </div>
            </div>
        }
        else
        {
            <div class="empty-state">
                <div class="empty-icon">💰</div>
                <p>No financials available</p>
            </div>
        }
    </div>
</div>