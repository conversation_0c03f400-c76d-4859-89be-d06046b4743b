﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Shared.CredentialsManager.Entities;
using Shared.CredentialsManager.Manager.Contracts;
using Shared.CredentialsManager.Manager.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.CredsManager.Pages
{
    public partial class ApplicationKeys : ComponentBase
    {
        [Inject] protected ICredentialsAdmin CredentialsAdmin { get; set; }
        [Inject] protected NavigationManager Navigation { get; set; }
        [Inject] protected IJSRuntime JSRuntime { get; set; }

        [Parameter] public string? ApplicationName { get; set; }

        protected List<ApplicationKey> applications = new();
        protected ApplicationKey? selectedApplicationKey;
        protected CreateApplicationModel newApplication = new();
        protected string searchTerm = string.Empty;
        protected string alertMessage = string.Empty;
        protected bool isError = false;
        protected bool isProcessing = false;

        protected IEnumerable<ApplicationKey> FilteredApplications =>
            string.IsNullOrWhiteSpace(searchTerm)
                ? applications
                : applications.Where(a => a.ApplicationName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));

        protected override async Task OnInitializedAsync()
        {
            await LoadApplications();

            // If navigated with a specific application name, select it
            if (!string.IsNullOrEmpty(ApplicationName))
            {
                var app = applications.FirstOrDefault(a => a.ApplicationName == ApplicationName);
                if (app != null)
                {
                    ViewApplicationDetails(app);
                }
            }
        }

        protected async Task LoadApplications()
        {
            try
            {
                applications = (await CredentialsAdmin.GetAllApplicationKeys()).ToList();
            }
            catch (Exception ex)
            {
                ShowAlert($"Error loading applications: {ex.Message}", true);
            }
        }

        protected async Task CreateApplication()
        {
            isProcessing = true;
            try
            {
                var createModel = new ApplicationKeyCreateModel
                { 
                    ApplicationName = newApplication.ApplicationName,
                    LastModifiedBy = null
                };
                var result = await CredentialsAdmin.CreateApplicationKey(createModel);
                applications.Add(result);
                applications = applications.OrderBy(a => a.ApplicationName).ToList();
                newApplication = new();
                ShowAlert($"Application '{result.ApplicationName}' created successfully!", false);
            }
            catch (Exception ex)
            {
                ShowAlert($"Error creating application: {ex.Message}", true);
            }
            finally
            {
                isProcessing = false;
            }
        }

        protected async Task ToggleApplicationStatus(ApplicationKey app)
        {
            try
            {
                var model = new ApplicationKeyUpdateModel
                {
                    ApplicationName = app.ApplicationName,
                    IsActive = !app.IsActive,
                    LastModifiedBy = null
                };

                var result = await CredentialsAdmin.UpdateApplicationKey(model);
                app.IsActive = result.IsActive;
                ShowAlert($"Application '{app.ApplicationName}' {(app.IsActive ? "activated" : "deactivated")} successfully!", false);
            }
            catch (Exception ex)
            {
                ShowAlert($"Error updating application: {ex.Message}", true);
            }
        }

        protected void ViewApplicationDetails(ApplicationKey app)
        {
            selectedApplicationKey = app;
        }

        protected void NavigateToSecrets(ApplicationKey app)
        {
            Navigation.NavigateTo($"/applicationsecrets?application={Uri.EscapeDataString(app.ApplicationName)}");
        }

        protected async Task CopyToClipboard(string text)
        {
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
            ShowAlert("Copied to clipboard!", false);
        }

        protected void ShowAlert(string message, bool error)
        {
            alertMessage = message;
            isError = error;
            StateHasChanged();
        }

        protected bool IsLegacyApplication(ApplicationKey applicationKey)
        {
            return applicationKey.EncryptedApplicationKey == null ||
                   applicationKey.EncryptedApplicationKey.Length == 0;
        }

        protected class CreateApplicationModel
        {
            [Required(ErrorMessage = "Application name is required")]
            [StringLength(255, ErrorMessage = "Application name cannot exceed 255 characters")]
            public string ApplicationName { get; set; } = string.Empty;
        }
    }
}