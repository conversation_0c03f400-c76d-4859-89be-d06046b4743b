﻿.widget {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    box-shadow: var(--shadow-);
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
    max-height: 350px;
    display: flex;
    flex-direction: column;
}

    .widget:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

/* Widget Header */
.widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right {
    display: flex;
    align-items: center;
}

.widget-icon {
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.active-badge {
    background: var(--bg-badge-neutral);
    color: var(--text-badge-neutral);
    border: 1px solid var(--text-badge-neutral);
    padding: 2px 12px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Widget Content with Scrolling */
.widget-content {
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Injury Table - Scrollable (matches contact-table pattern) */
.injury-table {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px;
    gap: 12px;
}

    /* Custom scrollbar styling */
    .injury-table::-webkit-scrollbar {
        width: 6px;
    }

    .injury-table::-webkit-scrollbar-track {
        background: var(--bg-tertiary);
    }

    .injury-table::-webkit-scrollbar-thumb {
        background: var(--primary-bg);
        border-radius: 3px;
    }

        .injury-table::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color-3);
        }

/* Injury Claims List - Remove scrolling from here */
.injury-claims-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Injury Claim Cards - Same as Vehicle Cards */
.injury-claim-card {
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: all 200ms ease;
}

    .injury-claim-card:hover {
        background: var(--primary-color-1);
        border-color: var(--primary-color-3);
        transform: translateY(-1px);
    }

/* Injury Header */
.injury-header {
    padding: 4px 16px;
    display: flex;
    align-items: center;
}

.person-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.person-icon {
    font-size: 12px;
    background: var(--icon-background);
}

.person-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Injury Details */
.injury-details {
    padding: 4px 8px;
}

.detail-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 12px;
    gap: 8px;
}

    .detail-row:last-child {
        margin-bottom: 0;
    }

.detail-label {
    color: var(--text-muted);
    min-width: 90px;
    flex-shrink: 0;
    font-weight: 500;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 500;
    text-align: right;
    flex: 1;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Medical Records Styling */
.medical-records.records {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Status Styling */
.status-active {
    color: var(--text-primary);
}

/* Loading and No Data States */
.loading-indicator {
    text-align: center;
    color: rgba(167, 169, 169, 1);
    font-style: italic;
    padding: 2rem;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: rgba(167, 169, 169, 0.7);
    text-align: center;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .header-right {
        align-self: flex-end;
    }

    .injury-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .detail-row {
        flex-direction: column;
        gap: 2px;
    }

    .detail-label {
        min-width: auto;
    }

    .detail-value {
        text-align: left;
    }
}