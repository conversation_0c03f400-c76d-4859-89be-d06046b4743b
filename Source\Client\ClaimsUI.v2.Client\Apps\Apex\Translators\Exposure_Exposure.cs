﻿using NGIC.Claims.Shared.Translator;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Translators
{
    public class Exposure_ExposureModel : BaseTranslator<Exposure, ExposureModel>
    {
        protected override Exposure DtoToEntity(Exposure entity, ExposureModel dto)
        {
            throw new NotImplementedException();
        }

        protected override ExposureModel EntityToDto(ExposureModel dto, Exposure entity)
        {
            // Exposure
            dto.AssignedAppraisalResource = entity.AssignedAppraisalResource;
            dto.EpicPropertyId = entity.EpicPropertyId;
            //dto.MedicalRecordsStatus
            //dto.Treatment
            //dto.TreatmentStatus
            dto.SequenceNumber = entity.SequenceNumber;

            // Contact
            ClaimContact claimContact = entity.ClaimContact;
            dto.Contact.AccidentRole = claimContact?.AccidentRole;
            if (claimContact?.Contact != null)
            {
                dto.Contact.FirstName = claimContact.Contact.FirstName;
                dto.Contact.LastName = claimContact.Contact.LastName;
            }
            else if (claimContact?.Vendor != null)
            {
                dto.Contact.OrganizationName = claimContact.Vendor.OrganizationName;
            }
            dto.Contact.LastContacted = claimContact?.LastContacted;
            dto.Contact.InjuryDescription = claimContact?.InjuryDescription;

            // Coverage
            dto.Coverage.CodeDescription = entity.Coverage?.CodeDescription ?? string.Empty;
            dto.Coverage.Deductible = entity.Coverage?.DeductiblePerIncident ?? entity.Coverage?.DeductiblePerPerson;
            dto.Coverage.Limit = entity.Coverage?.LimitPerIncident ?? entity.Coverage?.LimitPerPerson;

            return dto;
        }
    }
}