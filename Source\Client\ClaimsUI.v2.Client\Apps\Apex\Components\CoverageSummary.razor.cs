﻿using Microsoft.AspNetCore.Components;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Components
{
    public partial class CoverageSummary : ComponentBase
    {
        [Inject] private IHttpWrapper HttpDispatcher { get; set; }
        [Inject] private ITranslatorService Translator { get; set; }

        [Parameter] public ClaimSummaryModel ClaimSummary { get; set; }

        private CoverageSummaryModel coverageSummary => ClaimSummary?.CoverageSummary;
        private bool isLoading = false;

        protected override async Task OnInitializedAsync()
        {
            if (!ClaimSummary.IsWidgetLoaded("CoverageSummary"))
            {
                await LoadCoverageSummary();
            }
        }

        private async Task LoadCoverageSummary()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                // TODO: Replace with actual API call
                // coverageSummary = await HttpWrapper.Get<CoverageSummaryModel>($"/api/claims/{ClaimNumber}/coverage");

                // Mock data matching the screen
                await Task.Delay(400); // Simulate API delay
                ClaimSummary.CoverageSummary = GenerateCoverageSummary();
                ClaimSummary.MarkWidgetAsLoaded("CoverageSummary");
            }
            catch (Exception ex)
            {
                // TODO: Add proper logging
                Console.WriteLine($"Error loading coverage summary: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        // TODO: Remove everything below once we have the data
        // ***********************************************************
        private static CoverageSummaryModel GenerateCoverageSummary()
        {
            return new CoverageSummaryModel
            {
                BodilyInjury = GenerateBILimit(),
                PropertyDamage = GeneratePDLimit(),
                CoverageIssue = GenerateIssue(),
                Status = GenerateStatus()
            };
        }

        private static string GenerateBILimit()
        {
            Random random = new Random();
            string[] limits = { "$25K/$50K", "$50K/$100K", "$100K/$300K", "$250K/$500K" };
            return limits[random.Next(limits.Length)];
        }

        private static string GeneratePDLimit()
        {
            Random random = new Random();
            string[] limits = { "$25K", "$50K", "$100K", "$250K", "$500K" };
            return limits[random.Next(limits.Length)];
        }

        private static string GenerateIssue()
        {
            Random random = new Random();
            string[] issues = { "Policy Lapse", "Policy Cancellation", "Coverage Gap", "Vehicle Not Listed", "Excluded Driver" };
            return issues[random.Next(issues.Length)];
        }

        private static string GenerateStatus()
        {
            Random random = new Random();
            string[] statuses = { "Coverage Pending", "Coverage Question" };
            return statuses[random.Next(statuses.Length)];
        }
    }
}