﻿using NGIC.Claims.Shared.Translator;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.Exposure;
using NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Proxies.FunctionalCore;
using System;

namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Translators.ICAAP
{
    public class ExposureSummaryProxy_ExposureSummaryModel : BaseTranslator<Exposure, ExposureSummaryModel>
    {
        protected override Exposure DtoToEntity(Exposure entity, ExposureSummaryModel dto)
        {
            throw new NotImplementedException();
        }

        protected override ExposureSummaryModel EntityToDto(ExposureSummaryModel dto, Exposure entity)
        {
            dto.PropertyId = entity.PropertyId ?? 0; 
            dto.EpicAssignedToCCCId = entity.EpicAssignedToCCCId;
            dto.ExposureSequenceNumber = Convert.ToInt32(entity.ExposureSequenceNumber);
            dto.UnofficialCoverage = entity.UnofficialCoverage;
            dto.EpicAssignedEmployeeId = entity.AssignedEmployeeID;
            dto.EpicAssignedToFirstName = entity.EpicAssignedToFirstName;
            dto.EpicAssignedToLastName = entity.EpicAssignedToLastName;
            dto.EpicAssignedToEmail = entity.EpicAssignedToEmail;
            dto.EpicAssignedToPhone = entity.EpicAssignedToPhone;
            dto.EpicAssignedToRole = entity.EpicAssignedToRole;
            dto.CalcTimeStamp = entity.CalcTimeStamp;
            return dto;
        }
    }
}
