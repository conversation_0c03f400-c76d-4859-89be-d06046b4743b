﻿using NGIC.Claims.Common.Status.Response.Contracts;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Contracts;
using NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Contracts;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Proxy = NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Proxies.FunctionalCore;


namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Impl
{
    public class PropertyService : BaseService, IPropertyService
    {
        public PropertyService(
            IDispatcher dispatcher,
            ITranslatorService translator,
            IStatusResponse statusResponse
        ) : base(translator, dispatcher, statusResponse)
        {
        }
        public async Task<List<Property>> GetPropertiesByClaimId(string claimId)
        {
            List<Property> properties = new();

            IEnumerable<Proxy.Property> apexProperties = await Dispatcher.DispatchRequest<IEnumerable<Proxy.Property>, Proxy.IFunctionalCoreProxy>(
                                                    x => x.Property_GetApexPropertiesByClaimIdAsync(claimId));

            return apexProperties != null && apexProperties.Any()
                ? Translator.TranslateRangeDefault<Property>(apexProperties).ToList()
                : properties;
        }
    }
}