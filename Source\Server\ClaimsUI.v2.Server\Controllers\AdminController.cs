﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NGIC.Claims.Common.Status.Response.Contracts;
using Shared.CredentialsManager.Entities;
using Shared.CredentialsManager.Manager.Contracts;
using Shared.CredentialsManager.Manager.Models;
using System;
using System.Threading.Tasks;

namespace NGIC.Claims.Web.ClaimsUI.v2.Server.Controllers
{
    public class AdminController : BaseController<ICredentialsAdmin>
    {
        public AdminController(
            IStatusResponse statusResponse,
            IHttpContextAccessor httpContext,
            ICredentialsAdmin credentialsAdmin
        ) : base(credentialsAdmin, httpContext, statusResponse)
        {
        }

        [HttpGet]
        public async Task<IActionResult> GetAllApplicationKeys()
        {
            try
            {
                var result = await Service.GetAllApplicationKeys();
                return Ok(result);
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateApplicationKey([FromBody] ApplicationKeyCreateModel model)
        {
            try
            {
                model.LastModifiedBy = User?.Identity?.Name ?? "Unknown";
                var result = await Service.CreateApplicationKey(model);
                return Ok(result);
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateApplicationKey([FromBody] ApplicationKeyUpdateModel model)
        {
            try
            {
                model.LastModifiedBy = User?.Identity?.Name ?? "Unknown";
                var result = await Service.UpdateApplicationKey(model);
                return Ok(result);
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetApplicationKeyByName([FromQuery] string applicationName)
        {
            try
            {
                var result = await Service.GetApplicationKeyByName(applicationName);
                return Ok(result);
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetAllApplicationSecretsByApplicationName([FromQuery] string applicationName)
        {
            try
            {
                var result = await Service.GetAllApplicationSecretsByApplicationName(applicationName);
                return Ok(result);
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateApplicationSecret([FromBody] ApplicationSecretModel model)
        {
            try
            {
                model.LastModifiedBy = User?.Identity?.Name ?? "Unknown";
                var result = await Service.CreateApplicationSecret(model);
                return Ok(result);
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateApplicationSecret([FromBody] ApplicationSecretModel model)
        {
            try
            {
                model.LastModifiedBy = User?.Identity?.Name ?? "Unknown";
                var result = await Service.UpdateApplicationSecret(model);
                return Ok(result);
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetApplicationSecretByNameAndApplication([FromQuery] string applicationName, [FromRoute] string secretName)
        {
            try
            {
                var result = await Service.GetApplicationSecretByNameAndApplication(applicationName, secretName);
                return Ok(result);
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DecryptSecretValue([FromBody] ApplicationSecret secret)
        {
            try
            {
                var result = Service.DecryptSecretValue(secret);
                return Ok(new { value = result });
            }
            catch (Exception ex)
            {
                StatusResponse.StatusCode = "500";
                StatusResponse.StatusDescription = ex.Message;
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }

    public class CreateApplicationKeyRequest
    {
        public string ApplicationName { get; set; }
    }
}