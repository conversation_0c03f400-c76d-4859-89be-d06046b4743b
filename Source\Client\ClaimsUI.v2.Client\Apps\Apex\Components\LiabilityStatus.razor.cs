﻿using Microsoft.AspNetCore.Components;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Components
{
    public partial class LiabilityStatus : ComponentBase
    {
        [Inject] private IHttpDispatcher HttpDispatcher { get; set; }
        [Inject] private ITranslatorService Translator { get; set; }

        [Parameter] public ClaimSummaryModel ClaimSummary { get; set; }

        private LiabilityStatusModel liability => ClaimSummary?.LiabilityStatus;
        private bool isLoading = false;

        protected override async Task OnInitializedAsync()
        {
            if (!ClaimSummary.IsWidgetLoaded("LiabilityStatus"))
            {
                await LoadLiabilityData();
            }
        }

        private async Task LoadLiabilityData()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                // TODO: Replace with actual API call
                // liability = await HttpWrapper.Get<LiabilityModel>($"/api/claims/{ClaimSummary.ClaimNumber}/liability");

                // Mock data for now
                await Task.Delay(500); // Simulate API delay
                ClaimSummary.LiabilityStatus = GenerateLiability();
                ClaimSummary.LiabilityStatus.AtFaultPercentage = ClaimSummary.AtFault ?? ClaimSummary.LiabilityStatus.AtFaultPercentage;
                ClaimSummary.LiabilityStatus.Status = string.IsNullOrWhiteSpace(ClaimSummary.LiabilityStatus.AtFaultPercentage) ? "Pending" : "Determined";
                ClaimSummary.MarkWidgetAsLoaded("LiabilityStatus");
            }
            catch (Exception ex)
            {
                // TODO: Add proper logging
                Console.WriteLine($"Error loading liability data: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        // TODO: Remove everything below once we have the data
        // ***********************************************************
        private static LiabilityStatusModel GenerateLiability()
        {
            int completed = GenerateStatementsCompleted();
            return new LiabilityStatusModel
            {
                AtFaultPercentage = GenerateAtFaultPercentage(),
                PoliceReportStatus = GeneratePoliceReportStatus(),
                SIURedFlags = GenerateSIURedFlags(),
                StatementsCompleted = completed,
                StatementsTotal = GenerateStatementsTotal(completed),
            };
        }

        private static string GenerateAtFaultPercentage()
        {
            Random random = new Random();
            // Generate percentages in 25% increments for cleaner display
            string[] percentages = { 
                null, "5%", "10%", "15%", "20%", "25%", "30%", "35%", "40%", "45%", "50%",
                "55%", "60%", "65%", "70%", "75%", "80%", "85%", "90%", "95%", "100%", string.Empty
            };
            return percentages[random.Next(percentages.Length)];

        }

        private static string GeneratePoliceReportStatus()
        {
            Random random = new Random();
            string[] statuses = { "Requested", "Received", "No Police Response" };
            return statuses[random.Next(statuses.Length)];
        }

        private static int GenerateStatementsCompleted()
        {
            Random random = new Random();
            return random.Next(0, 5); // 0 to 4 completed statements
        }

        private static int GenerateStatementsTotal(int completed)
        {
            Random random = new Random();
            // Generate total statements that is equal to or greater than completed
            // Range from completed count to completed + 3 additional statements
            int maxAdditional = 3;
            return completed + random.Next(0, maxAdditional + 1);
        }

        private static List<string> GenerateSIURedFlags()
        {
            Random random = new Random();
            List<string> allFlags = new List<string>
            {
                "Late reporting (5+ days)",
                "Multiple recent claims",
                "Probability Score: 75%",
                "Inconsistent statements",
                "Prior fraud indicators",
                "Suspicious injury patterns",
                "Attorney involvement early",
                "Conflicting medical reports",
                "Vehicle value discrepancies",
                "Witness credibility issues"
            };

            // Randomly select 0-4 flags
            int flagCount = random.Next(0, 5);
            if (flagCount == 0)
            {
                return new List<string>();
            }

            List<string> selectedFlags = new List<string>();
            List<string> availableFlags = new List<string>(allFlags);

            for (int i = 0; i < flagCount; i++)
            {
                int index = random.Next(availableFlags.Count);
                selectedFlags.Add(availableFlags[index]);
                availableFlags.RemoveAt(index);
            }

            return selectedFlags;
        }
    }
}