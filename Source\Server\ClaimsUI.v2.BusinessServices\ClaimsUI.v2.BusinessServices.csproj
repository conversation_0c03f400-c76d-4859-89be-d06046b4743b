﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>NGIC.Claims.Web.ClaimsUI.v2.BusinessServices</AssemblyName>
    <RootNamespace>NGIC.Claims.Web.ClaimsUI.v2.BusinessServices</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Shared.CredentialsManager" Version="2.0.2" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ClaimsUI.v2.Data\ClaimsUI.v2.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Contracts\ICAAP\" />
    <Folder Include="Impl\ICAAP\" />
  </ItemGroup>

</Project>
