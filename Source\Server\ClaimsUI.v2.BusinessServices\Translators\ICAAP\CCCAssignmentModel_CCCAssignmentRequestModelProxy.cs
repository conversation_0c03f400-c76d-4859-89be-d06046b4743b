﻿using NGIC.Claims.Shared.Translator;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.AssignmentModels.Vendor;
using NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Proxies.FunctionalCore;

namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Translators.ICAAP
{
    public class CCCAssignmentModel_CCCAssignmentRequestModelProxy : BaseTranslator<CCCAssignmentRequestModel, CCCAssignmentModel>
    {
        protected override CCCAssignmentRequestModel DtoToEntity(CCCAssignmentRequestModel entity, CCCAssignmentModel dto)
        {
            entity.ClaimId = dto.EpicClaimId;
            entity.ExposureId = dto.ExposureId;
            entity.ClaimNumber = dto.ClaimNumber;
            entity.ExposureSequenceNumber = dto.ExposureSequenceNumber;
            entity.AssignmentRecipientType = dto.AssignmentRecipientType;
            entity.AssignmentRecipientId = dto.AssignmentRecipientId;
            entity.AssignmentRecipientCCCId = dto.AssignmentRecipientCCCId;
            entity.InspectionMethod = dto.InspectionMethod;
            entity.InspectionType = dto.InspectionType;
            entity.InstructionsToEstimator = dto.InstructionsToEstimator;
            entity.ClaimTypeCode = dto.ClaimTypeCode;
            entity.AdjusterId = dto.AdjusterId;
            entity.AdjusterCCCId = dto.AdjusterCCCId;
            entity.AdjusterFirstName = dto.AdjusterFirstName;
            entity.AdjusterLastName = dto.AdjusterLastName;
            entity.AdjusterPhone = dto.AdjusterPhone;
            entity.AdjusterEmail = dto.AdjusterEmail;
            entity.AppraiserFirstName = dto.AppraiserFirstName;
            entity.AppraiserLastName = dto.AppraiserLastName;
            entity.AppraiserPhone = dto.AppraiserPhone;
            entity.AppraiserType = dto.AppraiserType;
            entity.VinNumber = dto.VINNumber;
            entity.LicensePlate = dto.LicensePlate;
            entity.LicenseState = dto.LicenseState;
            entity.VehicleYear = dto.VehicleYear;
            entity.Make = dto.Make;
            entity.Model = dto.Model;
            entity.VehicleUsage = dto.VehicleUsage;
            entity.Style = dto.Style;
            entity.Color = dto.Color;
            entity.VehicleType = dto.VehicleType;
            entity.PriorDamages = dto.PriorDamages;
            entity.DamageDescription = dto.DamageDescription;
            entity.IsDrivable = dto.IsDrivable;
            entity.DamageExtent = dto.DamageExtent;
            entity.VehicleLocationType = dto.VehicleLocationType;
            entity.PhysicalLocationAddress1 = dto.PhysicalLocationAddress1;
            entity.PhysicalLocationAddressName = dto.PhysicalLocationAddressName;
            entity.PhysicalLocationCity = dto.PhysicalLocationCity;
            entity.PhysicalLocationCounty = dto.PhysicalLocationCounty;
            entity.PhysicalLocationStateCode = dto.PhysicalLocationStateCode;
            entity.PhysicalLocationZipCode = dto.PhysicalLocationZipCode;
            entity.PhysicalLocationPhone = dto.PhysicalLocationPhone;
            entity.Dmg1Front = dto.Dmg1Front;
            entity.Dmg1LeftFront = dto.Dmg1LeftFront;
            entity.Dmg1RightFront = dto.Dmg1RightFront;
            entity.Dmg1Rear = dto.Dmg1Rear;
            entity.Dmg1LeftRear = dto.Dmg1LeftRear;
            entity.Dmg1RightRear = dto.Dmg1RightRear;
            entity.Dmg1LeftSide = dto.Dmg1LeftSide;
            entity.Dmg1RightSide = dto.Dmg1RightSide;
            entity.Dmg1Undercarriage = dto.Dmg1Undercarriage;
            entity.Dmg1RoofTop = dto.Dmg1RoofTop;
            entity.Dmg1Hood = dto.Dmg1Hood;
            entity.Dmg1Mechanical = dto.Dmg1Mechanical;
            entity.Dmg1Electrical = dto.Dmg1Electrical;
            entity.Dmg1Interior = dto.Dmg1Interior;
            entity.Dmg1AwningOnly = dto.Dmg1AwningOnly;
            entity.Dmg1RollOver = dto.Dmg1RollOver;
            entity.Dmg2Front2 = dto.Dmg2Front2;
            entity.Dmg2LeftFront = dto.Dmg2LeftFront;
            entity.Dmg2RightFront = dto.Dmg2RightFront;
            entity.Dmg2Rear2 = dto.Dmg2Rear2;
            entity.Dmg2LeftRear = dto.Dmg2LeftRear;
            entity.Dmg2RightRear = dto.Dmg2RightRear;
            entity.Dmg2LeftSide = dto.Dmg2LeftSide;
            entity.Dmg2RightSide = dto.Dmg2RightSide;
            entity.Dmg2Undercarriage = dto.Dmg2Undercarriage;
            entity.Dmg2RoofTop = dto.Dmg2RoofTop;
            entity.Dmg2Hood = dto.Dmg2Hood;
            entity.Dmg2Mechanical = dto.Dmg2Mechanical;
            entity.Dmg2Electrical = dto.Dmg2Electrical;
            entity.Dmg2Interior = dto.Dmg2Interior;
            entity.Dmg2AwningOnly = dto.Dmg2AwningOnly;
            entity.Dmg2RollOver = dto.Dmg2RollOver;
            entity.CpRole = dto.CPRole;
            entity.PrimaryContactFirstName = dto.PrimaryContactFirstName;
            entity.PrimaryContactLastName = dto.PrimaryContactLastName;
            entity.PrimaryContactPhone = dto.PrimaryContactPhone;
            entity.PrimaryVendorFullName = dto.PrimaryVendorFullName;
            entity.VendorName = dto.VendorName;
            entity.ClaimantFirstName = dto.ClaimantFirstName;
            entity.ClaimantLastName = dto.ClaimantLastName;
            entity.ClaimantContactType = dto.ClaimantContactType;
            entity.ClaimantCellPhone = dto.ClaimantCellPhone;
            entity.ClaimantBusinessPhone = dto.ClaimantBusinessPhone;
            entity.ClaimantHomePhone = dto.ClaimantHomePhone;
            entity.ClaimantAddressName = dto.ClaimantAddressName;
            entity.ClaimantEmail = dto.ClaimantEmail;
            entity.ClaimantCity = dto.ClaimantCity;
            entity.ClaimantCounty = dto.ClaimantCounty;
            entity.ClaimantStateCode = dto.ClaimantStateCode;
            entity.ClaimantZipCode = dto.ClaimantZipCode;
            entity.InjuryType = dto.InjuryType;
            entity.LossDescription = dto.LossDescription;
            entity.LossDateTime = dto.LossDateTime;
            entity.LossReportDateTime = dto.LossReportDateTime.GetValueOrDefault();
            entity.LossLocationAddress1 = dto.LossLocationAddress1;
            entity.LossLocationCity = dto.LossLocationCity;
            entity.LossLocationCounty = dto.LossLocationCounty;
            entity.LossLocationStateCode = dto.LossLocationStateCode;
            entity.LossLocationZipCode = dto.LossLocationZipCode;
            entity.Deductible = dto.Deductible;
            entity.PolicyStateCode = dto.PolicyStateCode;
            entity.PolicyStartDate = dto.PolicyStartDate.GetValueOrDefault();
            entity.PolicyExpirationDate = dto.PolicyExpirationDate.GetValueOrDefault();
            entity.PolicySubType = dto.PolicySubType;
            entity.UnderwritingCompany = dto.UnderwritingCompany;
            entity.AssignmentType = dto.AssignmentType;
            entity.ReserveCategory = dto.ReserveCategory;
            entity.PolicyProduct = dto.PolicyProduct;
            entity.IcaapLoggedInUser = dto.IcaapLoggedInUser;
            return entity;
        }

        protected override CCCAssignmentModel EntityToDto(CCCAssignmentModel dto, CCCAssignmentRequestModel entity)
        {
            throw new System.NotImplementedException();
        }
    }
}
