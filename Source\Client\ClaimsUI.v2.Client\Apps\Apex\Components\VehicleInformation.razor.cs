﻿using Microsoft.AspNetCore.Components;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Components
{
    public partial class VehicleInformation : ComponentBase
    {
        [Inject] private IHttpDispatcher HttpDispatcher { get; set; }
        [Inject] private ITranslatorService Translator { get; set; }

        [Parameter] public ClaimSummaryModel ClaimSummary { get; set; }

        private List<VehicleInformationModel> vehicles => ClaimSummary?.Vehicles;
        private bool isLoading = false;

        protected override async Task OnInitializedAsync()
        {
            if (!ClaimSummary.IsWidgetLoaded("VehicleInformation"))
            {
                await LoadVehicleInformation();
            }
        }

        private async Task LoadVehicleInformation()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                // Get Properties
                IEnumerable<Property> epicProperties = await GetEpicPropertiesByClaimId(ClaimSummary.ClaimId);
                ClaimSummary.Vehicles = Translator.TranslateRange<VehicleInformationModel>(epicProperties).ToList();

                // TODO: Get rid of this if statement
                if (!ClaimSummary.Vehicles.Any())
                {
                    await Task.Delay(500); // Simulate API delay
                    ClaimSummary.Vehicles = GenerateVehicles();
                }
                else
                {
                    // TODO: Get rid of this once we have the parameters
                    foreach (VehicleInformationModel vehicle in ClaimSummary.Vehicles)
                    {
                        // Check for MOI
                        bool hasMOI = ClaimSummary.Exposures.FirstOrDefault(x => x.EpicPropertyId == vehicle.EpicPropertyId 
                                            && !string.IsNullOrWhiteSpace(x.AssignedAppraisalResource)) != null ? true : false;
                        vehicle.IsEstimate = new Random().Next(2) == 1;
                        vehicle.IsICAAPComplete = hasMOI ? true : false;
                        vehicle.EstimateAmount = vehicle.IsEstimate ? GenerateAmount() : null;
                        vehicle.IsTotalLoss = vehicle.IsICAAPComplete ? !hasMOI ? true : false : null;
                        vehicle.Location = GenerateLocation();
                        vehicle.Rental = GenerateRental();
                        vehicle.Storage = GenerateStorage();
                        vehicle.SupplementStatus = GenerateSupplementStatus();
                    }
                }
                ClaimSummary.MarkWidgetAsLoaded("VehicleInformation");
            }
            catch (Exception ex)
            {
                // TODO: Add proper logging
                Console.WriteLine($"Error loading vehicle information: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private async Task<IEnumerable<Property>> GetEpicPropertiesByClaimId(string claimId)
        {
            return await HttpDispatcher.Get<List<Property>>($"{Routes.Property.GetPropertiesByClaimId}{claimId}");
        }

        private async Task RefreshProperties()
        {
            await LoadVehicleInformation();
        }

        // TODO: Remove everything below once we have the data
        // ***********************************************************
        private static List<VehicleInformationModel> GenerateVehicles()
        {
            Random random = new();
            List<VehicleInformationModel> vehicles = new();
            for (int i = 0; i < 2; i++)
            {
                bool icaap = random.Next(2) == 1;
                bool estimate = random.Next(2) == 1;
                
                vehicles.Add(new VehicleInformationModel
                {
                    Description = GenerateVehicleDescription(),
                    EstimateAmount = estimate ? GenerateAmount() : null,
                    IsEstimate = estimate,
                    IsICAAPComplete = icaap,
                    IsTotalLoss = icaap ? new Random().Next(2) == 1 : null,
                    Location = GenerateLocation(),
                    Rental = random.Next(2) == 1 ? GenerateRental() : null,
                    Storage = random.Next(2) == 1 ? GenerateStorage() : null,
                    SupplementStatus = GenerateSupplementStatus(),
                    Type = i==0 ? "Insured" : "Claimant"
                });
            }
            return vehicles;
        }

        private static decimal GenerateAmount()
        {
            var random = new Random();

            // Generate random amount between $500 and $25,000
            int minAmount = 500;
            int maxAmount = 25000;

            // Generate random amount in the range
            decimal randomAmount = random.Next(minAmount, maxAmount + 1);

            // Round to nearest $10
            return Math.Round(randomAmount / 10) * 10;
        }
        
        private static Location GenerateLocation()
        {
            Random random = new Random();

            // Dictionary of states with their abbreviations and 10 major cities each
            Dictionary<string, (string abbrev, string[] cities)> statesAndCities = new Dictionary<string, (string abbrev, string[] cities)>
            {
                ["Alabama"] = ("AL", new[] { "Birmingham", "Montgomery", "Mobile", "Huntsville", "Tuscaloosa", "Hoover", "Dothan", "Auburn", "Decatur", "Madison" }),
                ["Alaska"] = ("AK", new[] { "Anchorage", "Fairbanks", "Juneau", "Sitka", "Ketchikan", "Wasilla", "Kenai", "Kodiak", "Bethel", "Palmer" }),
                ["Arizona"] = ("AZ", new[] { "Phoenix", "Tucson", "Mesa", "Chandler", "Scottsdale", "Glendale", "Gilbert", "Tempe", "Peoria", "Surprise" }),
                ["Arkansas"] = ("AR", new[] { "Little Rock", "Fayetteville", "Fort Smith", "Springdale", "Jonesboro", "North Little Rock", "Conway", "Rogers", "Pine Bluff", "Bentonville" }),
                ["California"] = ("CA", new[] { "Los Angeles", "San Diego", "San Jose", "San Francisco", "Fresno", "Sacramento", "Long Beach", "Oakland", "Bakersfield", "Anaheim" }),
                ["Colorado"] = ("CO", new[] { "Denver", "Colorado Springs", "Aurora", "Fort Collins", "Lakewood", "Thornton", "Arvada", "Westminster", "Pueblo", "Centennial" }),
                ["Connecticut"] = ("CT", new[] { "Bridgeport", "New Haven", "Hartford", "Stamford", "Waterbury", "Norwalk", "Danbury", "New Britain", "West Hartford", "Greenwich" }),
                ["Delaware"] = ("DE", new[] { "Wilmington", "Dover", "Newark", "Middletown", "Smyrna", "Milford", "Seaford", "Georgetown", "Elsmere", "New Castle" }),
                ["Florida"] = ("FL", new[] { "Jacksonville", "Miami", "Tampa", "Orlando", "St. Petersburg", "Hialeah", "Tallahassee", "Fort Lauderdale", "Port St. Lucie", "Cape Coral" }),
                ["Georgia"] = ("GA", new[] { "Atlanta", "Augusta", "Columbus", "Macon", "Savannah", "Athens", "Sandy Springs", "Roswell", "Johns Creek", "Albany" }),
                ["Hawaii"] = ("HI", new[] { "Honolulu", "Pearl City", "Hilo", "Kailua", "Waipahu", "Kaneohe", "Mililani", "Kahului", "Ewa Gentry", "Mililani Town" }),
                ["Idaho"] = ("ID", new[] { "Boise", "Meridian", "Nampa", "Idaho Falls", "Pocatello", "Caldwell", "Coeur d'Alene", "Twin Falls", "Lewiston", "Post Falls" }),
                ["Illinois"] = ("IL", new[] { "Chicago", "Aurora", "Rockford", "Joliet", "Naperville", "Springfield", "Peoria", "Elgin", "Waukegan", "Cicero" }),
                ["Indiana"] = ("IN", new[] { "Indianapolis", "Fort Wayne", "Evansville", "South Bend", "Carmel", "Fishers", "Bloomington", "Hammond", "Gary", "Muncie" }),
                ["Iowa"] = ("IA", new[] { "Des Moines", "Cedar Rapids", "Davenport", "Sioux City", "Iowa City", "Waterloo", "Council Bluffs", "Ames", "Dubuque", "West Des Moines" }),
                ["Kansas"] = ("KS", new[] { "Wichita", "Overland Park", "Kansas City", "Topeka", "Olathe", "Lawrence", "Shawnee", "Salina", "Hutchinson", "Lenexa" }),
                ["Kentucky"] = ("KY", new[] { "Louisville", "Lexington", "Bowling Green", "Owensboro", "Covington", "Richmond", "Georgetown", "Florence", "Hopkinsville", "Nicholasville" }),
                ["Louisiana"] = ("LA", new[] { "New Orleans", "Baton Rouge", "Shreveport", "Lafayette", "Lake Charles", "Kenner", "Bossier City", "Monroe", "Alexandria", "Houma" }),
                ["Maine"] = ("ME", new[] { "Portland", "Lewiston", "Bangor", "South Portland", "Auburn", "Biddeford", "Sanford", "Saco", "Augusta", "Westbrook" }),
                ["Maryland"] = ("MD", new[] { "Baltimore", "Frederick", "Rockville", "Gaithersburg", "Bowie", "Hagerstown", "Annapolis", "College Park", "Salisbury", "Laurel" }),
                ["Massachusetts"] = ("MA", new[] { "Boston", "Worcester", "Springfield", "Lowell", "Cambridge", "New Bedford", "Brockton", "Quincy", "Lynn", "Fall River" }),
                ["Michigan"] = ("MI", new[] { "Detroit", "Grand Rapids", "Warren", "Sterling Heights", "Lansing", "Ann Arbor", "Flint", "Dearborn", "Livonia", "Westland" }),
                ["Minnesota"] = ("MN", new[] { "Minneapolis", "St. Paul", "Rochester", "Duluth", "Bloomington", "Brooklyn Park", "Plymouth", "St. Cloud", "Eagan", "Woodbury" }),
                ["Mississippi"] = ("MS", new[] { "Jackson", "Gulfport", "Southaven", "Hattiesburg", "Biloxi", "Meridian", "Tupelo", "Greenville", "Olive Branch", "Horn Lake" }),
                ["Missouri"] = ("MO", new[] { "Kansas City", "St. Louis", "Springfield", "Independence", "Columbia", "Lee's Summit", "O'Fallon", "St. Joseph", "St. Charles", "St. Peters" }),
                ["Montana"] = ("MT", new[] { "Billings", "Missoula", "Great Falls", "Bozeman", "Butte", "Helena", "Kalispell", "Havre", "Anaconda", "Miles City" }),
                ["Nebraska"] = ("NE", new[] { "Omaha", "Lincoln", "Bellevue", "Grand Island", "Kearney", "Fremont", "Hastings", "North Platte", "Norfolk", "Columbus" }),
                ["Nevada"] = ("NV", new[] { "Las Vegas", "Henderson", "Reno", "North Las Vegas", "Sparks", "Carson City", "Fernley", "Elko", "Mesquite", "Boulder City" }),
                ["New Hampshire"] = ("NH", new[] { "Manchester", "Nashua", "Concord", "Derry", "Rochester", "Salem", "Dover", "Merrimack", "Londonderry", "Hudson" }),
                ["New Jersey"] = ("NJ", new[] { "Newark", "Jersey City", "Paterson", "Elizabeth", "Edison", "Woodbridge", "Lakewood", "Toms River", "Hamilton", "Trenton" }),
                ["New Mexico"] = ("NM", new[] { "Albuquerque", "Las Cruces", "Rio Rancho", "Santa Fe", "Roswell", "Farmington", "Clovis", "Hobbs", "Alamogordo", "Carlsbad" }),
                ["New York"] = ("NY", new[] { "New York City", "Buffalo", "Rochester", "Yonkers", "Syracuse", "Albany", "New Rochelle", "Mount Vernon", "Schenectady", "Utica" }),
                ["North Carolina"] = ("NC", new[] { "Charlotte", "Raleigh", "Greensboro", "Durham", "Winston-Salem", "Fayetteville", "Cary", "Wilmington", "High Point", "Asheville" }),
                ["North Dakota"] = ("ND", new[] { "Fargo", "Bismarck", "Grand Forks", "Minot", "West Fargo", "Williston", "Dickinson", "Mandan", "Jamestown", "Wahpeton" }),
                ["Ohio"] = ("OH", new[] { "Columbus", "Cleveland", "Cincinnati", "Toledo", "Akron", "Dayton", "Parma", "Canton", "Youngstown", "Lorain" }),
                ["Oklahoma"] = ("OK", new[] { "Oklahoma City", "Tulsa", "Norman", "Broken Arrow", "Lawton", "Edmond", "Moore", "Midwest City", "Enid", "Stillwater" }),
                ["Oregon"] = ("OR", new[] { "Portland", "Eugene", "Salem", "Gresham", "Hillsboro", "Bend", "Beaverton", "Medford", "Springfield", "Corvallis" }),
                ["Pennsylvania"] = ("PA", new[] { "Philadelphia", "Pittsburgh", "Allentown", "Erie", "Reading", "Scranton", "Bethlehem", "Lancaster", "Harrisburg", "Altoona" }),
                ["Rhode Island"] = ("RI", new[] { "Providence", "Warwick", "Cranston", "Pawtucket", "East Providence", "Woonsocket", "Newport", "Central Falls", "Westerly", "North Providence" }),
                ["South Carolina"] = ("SC", new[] { "Charleston", "Columbia", "North Charleston", "Mount Pleasant", "Rock Hill", "Greenville", "Summerville", "Sumter", "Goose Creek", "Hilton Head" }),
                ["South Dakota"] = ("SD", new[] { "Sioux Falls", "Rapid City", "Aberdeen", "Brookings", "Watertown", "Mitchell", "Yankton", "Pierre", "Huron", "Vermillion" }),
                ["Tennessee"] = ("TN", new[] { "Nashville", "Memphis", "Knoxville", "Chattanooga", "Clarksville", "Murfreesboro", "Franklin", "Johnson City", "Bartlett", "Kingsport" }),
                ["Texas"] = ("TX", new[] { "Houston", "San Antonio", "Dallas", "Austin", "Fort Worth", "El Paso", "Arlington", "Corpus Christi", "Plano", "Lubbock" }),
                ["Utah"] = ("UT", new[] { "Salt Lake City", "West Valley City", "Provo", "West Jordan", "Orem", "Sandy", "Ogden", "St. George", "Layton", "Taylorsville" }),
                ["Vermont"] = ("VT", new[] { "Burlington", "Essex", "South Burlington", "Colchester", "Rutland", "Bennington", "Brattleboro", "Milton", "Hartford", "Barre" }),
                ["Virginia"] = ("VA", new[] { "Virginia Beach", "Norfolk", "Chesapeake", "Richmond", "Newport News", "Alexandria", "Hampton", "Portsmouth", "Suffolk", "Roanoke" }),
                ["Washington"] = ("WA", new[] { "Seattle", "Spokane", "Tacoma", "Vancouver", "Bellevue", "Kent", "Everett", "Renton", "Federal Way", "Spokane Valley" }),
                ["West Virginia"] = ("WV", new[] { "Charleston", "Huntington", "Parkersburg", "Morgantown", "Wheeling", "Weirton", "Fairmont", "Martinsburg", "Beckley", "Clarksburg" }),
                ["Wisconsin"] = ("WI", new[] { "Milwaukee", "Madison", "Green Bay", "Kenosha", "Racine", "Appleton", "Waukesha", "Oshkosh", "Eau Claire", "Janesville" }),
                ["Wyoming"] = ("WY", new[] { "Cheyenne", "Casper", "Laramie", "Gillette", "Rock Springs", "Sheridan", "Green River", "Evanston", "Riverton", "Jackson" })
            };

            // Get random state
            string[] stateNames = statesAndCities.Keys.ToArray();
            string selectedState = stateNames[random.Next(stateNames.Length)];

            // Get random city from that state
            var stateInfo = statesAndCities[selectedState];
            string selectedCity = stateInfo.cities[random.Next(stateInfo.cities.Length)];

            return new Location { City = selectedCity, Name = GenerateLocationName(), State = stateInfo.abbrev };
        }

        private static string GenerateLocationName()
        {
            Random random = new Random();
            return LocationNames[random.Next(LocationNames.Length)];
        }

        private static readonly string[] LocationNames = {
            "ABC Auto Repair","Precision Collision Center","Mike's Body Shop","Elite Auto Works","Metro Collision Repair","Johnson's Automotive","ProFix Auto Body",
            "Central Auto Clinic","Premium Paint & Body","Quality Collision Services","Express Auto Repair","Advanced Bodyworks","City Auto Center","Superior Collision",
            "Martinez Auto Body","Northside Repair Shop","Universal Auto Care","Diamond Auto Body","Reliable Collision Center","Thompson's Auto Repair","Eagle Auto Works",
            "First Class Collision","Anderson Body Shop","Peak Performance Auto","Westside Auto Repair","Crown Collision Center","Garcia's Auto Body","Professional Auto Works",
            "Eastside Collision","Miller's Body Shop","Apex Auto Repair","Golden Gate Auto Body","Wilson Collision Center","Premier Auto Care","Southpoint Auto Repair",
            "Liberty Body Shop","Champion Collision","Davis Auto Works","Platinum Auto Body","Riverside Repair Shop","Turner's Collision Center","Crossroads Auto Body",
            "Heritage Auto Repair","Summit Collision Services","Valley Auto Works","Coastal Body Shop","Tri-State Auto Repair","Mountain View Collision",
            "Parker's Auto Body","Horizon Auto Center"
        };

        private static Rental GenerateRental()
        {
            Random random = new Random();
            string[] rentalCompanies = { "Enterprise", "Hertz", "Avis", "Budget", "Alamo", "National", "Thrifty", "Dollar", "Sixt", "Fox Rent A Car" };
            string selectedCompany = rentalCompanies[random.Next(rentalCompanies.Length)];
            // Generate random duration between 1 and 30 days
            int durationDays = random.Next(1, 31);
            return new Rental { Company = selectedCompany, Duration = $"{durationDays} days" };
        }

        private static Storage GenerateStorage()
        {
            Random random = new Random();
            // Generate random cost between $50 and $500
            decimal cost = Math.Round(random.Next(50, 501) / 10.0m) * 10; // Round to nearest $10
            // Generate random duration between 1 and 30 days
            int durationDays = random.Next(1, 31);
            return new Storage { Authorization = true, Cost = $"${cost}", Duration = $"{durationDays} days" };
        }

        private static string GenerateSupplementStatus()
        {
            Random random = new Random();
            string[] statuses = { "No Supplement", "Supplement Likely", "Has Supplement" };
            return statuses[random.Next(statuses.Length)];
        }

        private static string GenerateVehicleDescription()
        {
            {
                var random = new Random();

                // Generate year within last 30 years (1995-2024, assuming current year is 2024)
                int currentYear = DateTime.Now.Year;
                int randomYear = random.Next(currentYear - 29, currentYear + 1); // Last 30 years inclusive

                // Dictionary of 10 popular makes with up to 10 models each
                var makesAndModels = new Dictionary<string, string[]>
                {
                    ["Toyota"] = new[] { "Camry", "Corolla", "RAV4", "Highlander", "Prius", "Tacoma", "Tundra", "Sienna", "4Runner", "Avalon" },
                    ["Honda"] = new[] { "Civic", "Accord", "CR-V", "Pilot", "Odyssey", "Fit", "HR-V", "Passport", "Ridgeline" },
                    ["Ford"] = new[] { "F-150", "Escape", "Explorer", "Focus", "Fusion", "Mustang", "Expedition", "Ranger", "Bronco" },
                    ["Chevrolet"] = new[] { "Silverado", "Equinox", "Malibu", "Traverse", "Tahoe", "Suburban", "Camaro", "Cruze", "Impala", "Colorado" },
                    ["Nissan"] = new[] { "Altima", "Sentra", "Rogue", "Pathfinder", "Maxima", "Frontier", "Titan", "Murano", "Armada", "Versa" },
                    ["Hyundai"] = new[] { "Elantra", "Sonata", "Tucson", "Santa Fe", "Accent",  "Veloster", "Palisade", "Ioniq" },
                    ["Kia"] = new[] { "Optima", "Forte", "Sorento", "Sportage", "Soul", "Rio",  "Telluride" },
                    ["Subaru"] = new[] { "Outback", "Forester", "Impreza", "Legacy" },
                    ["Jeep"] = new[] { "Wrangler", "Grand Cherokee", "Cherokee", "Compass", "Renegade", "Gladiator", "Commander", "Liberty", "Patriot", "Grand Wagoneer" },
                    ["BMW"] = new[] { "3 Series", "5 Series", "X3", "X5", "7 Series", "X1", "4 Series", "2 Series", "X7", "Z4" }
                };

                // Select random make
                var makes = makesAndModels.Keys.ToArray();
                string selectedMake = makes[random.Next(makes.Length)];

                // Select random model from that make
                string[] models = makesAndModels[selectedMake];
                string selectedModel = models[random.Next(models.Length)];

                return $"{randomYear} {selectedMake} {selectedModel}";
            }
        }
    }
}