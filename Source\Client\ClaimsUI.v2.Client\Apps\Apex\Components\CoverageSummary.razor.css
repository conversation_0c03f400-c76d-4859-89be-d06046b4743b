﻿.widget {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
    max-height: 350px;
    display: flex;
    flex-direction: column;
}

    .widget:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

/* Widget Header */
.widget-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
}

.widget-icon {
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
}

/* Widget Content */
.widget-content {
    padding: 0;
}

/* Coverage Table - Similar to Dashboard Tables */
.coverage-table {
    display: flex;
    flex-direction: column;
    padding: 16px;
}

.coverage-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-light);
    transition: background-color 0.2s ease;
}

    .coverage-row:last-child {
        border-bottom: none;
    }

    .coverage-row:hover {
        background: var(--primary-color-1);
    }

.coverage-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
}

.coverage-value {
    font-size: 14px;
    font-weight: 550;
    color: var(--text-primary);
    text-align: right;
}

/* Coverage Issue Styling */
.coverage-issue .coverage-issue-text {
    display: flex;
    align-items: center;
    gap: 4px;
}

.coverage-issue .coverage-no-issue {
    color: var(--text-muted);
    font-style: italic;
}

/* Loading and No Data States */
.loading-indicator {
    text-align: center;
    color: rgba(167, 169, 169, 1);
    font-style: italic;
    padding: 2rem;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
    text-align: center;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .header-right {
        align-self: flex-end;
    }

    .coverage-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px 16px;
    }

    .coverage-value {
        text-align: left;
        width: 100%;
    }
}