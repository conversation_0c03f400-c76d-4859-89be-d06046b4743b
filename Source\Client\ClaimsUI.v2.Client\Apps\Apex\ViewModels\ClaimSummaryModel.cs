﻿namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels
{
    public class ClaimSummaryModel
    {
        // Existing properties
        public string AtFault { get; set; }
        public string ClaimId { get; set; }
        public string ClaimNumber { get; set; }
        public string PolicyNumber { get; set; }
        public string InsuredName { get; set; }
        public DateTime? IncidentDate { get; set; }
        public string Status { get; set; }
        public bool IsMyClaim { get; set; }

        // Component data properties
        public List<ContactStatusModel> Contacts { get; set; } = new();
        public CoverageSummaryModel CoverageSummary { get; set; } = new();
        public List<ExposureModel> Exposures { get; set; } = new();
        public FinancialSummaryModel FinancialSummary { get; set; } = new();
        public List<InjuryClaimsModel> InjuryClaims { get; set; } = new();
        public LiabilityStatusModel LiabilityStatus { get; set; } = new();
        public List<VehicleInformationModel> Vehicles { get; set; } = new();

        // Track which widgets have loaded data to prevent reloading
        public HashSet<string> LoadedWidgets { get; set; } = new();
        public bool IsWidgetLoaded(string widgetName) => LoadedWidgets.Contains(widgetName);
        public void MarkWidgetAsLoaded(string widgetName) => LoadedWidgets.Add(widgetName);
        public void ForceWidgetReload(string widgetName) => LoadedWidgets.Remove(widgetName);
    }
}