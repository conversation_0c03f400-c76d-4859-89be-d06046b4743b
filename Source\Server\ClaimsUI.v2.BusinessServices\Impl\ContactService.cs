﻿using NGIC.Claims.Common.Status.Response.Contracts;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Contracts;
using NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Contracts;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Proxy = NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Proxies.FunctionalCore;


namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Impl
{
    public class ContactService : BaseService, IContactService
    {
        public ContactService(
            IDispatcher dispatcher,
            ITranslatorService translator,
            IStatusResponse statusResponse
        ) : base(translator, dispatcher, statusResponse)
        {
        }
        public async Task<List<ClaimContact>> GetClaimContactsByClaimNumber(string claimNumber)
        {
            List<ClaimContact> claimContacts = new();

            IEnumerable<Proxy.ClaimContact> apexContacts = await Dispatcher.DispatchRequest<IEnumerable<Proxy.ClaimContact>, Proxy.IFunctionalCoreProxy>(
                                                    x => x.Contact_GetApexContactsByClaimNumberAsync(claimNumber));

            return apexContacts != null && apexContacts.Any()
                ? Translator.TranslateRangeDefault<ClaimContact>(apexContacts).ToList()
                : claimContacts;
        }
    }
}