﻿using Microsoft.AspNetCore.Components;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Components
{
    public partial class FinancialSummary : ComponentBase
    {
        [Inject] private IHttpDispatcher HttpDispatcher { get; set; }
        [Inject] private ITranslatorService Translator { get; set; }

        [Parameter] public ClaimSummaryModel ClaimSummary { get; set; }

        private FinancialSummaryModel financialSummary => ClaimSummary?.FinancialSummary;
        private bool isLoading = false;


        protected override async Task OnInitializedAsync()
        {
            if (!ClaimSummary.IsWidgetLoaded("FinancialSummary"))
            {
                await LoadFinancialSummary();
            }
        }

        private async Task LoadFinancialSummary()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                // TODO: Replace with actual API call
                // financialSummary = await HttpWrapper.Get<FinancialSummary>($"/api/claims/{ClaimNumber}/financial");

                // Mock data for now
                await Task.Delay(400);
                ClaimSummary.FinancialSummary = new FinancialSummaryModel
                {
                    Reserve = 15000,
                    Paid = 2500,
                    Outstanding = 12500,
                    Deductible = 500
                };
                ClaimSummary.MarkWidgetAsLoaded("FinancialSummary");
            }
            catch (Exception ex)
            {
                // TODO: Add proper logging
                Console.WriteLine($"Error loading financial summary: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }
    }
}