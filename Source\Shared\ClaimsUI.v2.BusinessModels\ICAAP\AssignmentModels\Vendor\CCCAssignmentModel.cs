﻿using System;

namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.AssignmentModels.Vendor
{
    public class CCCAssignmentModel : BaseModel
    {
        public int ClaimId { get; set; }
        public int ContactId { get; set; }
        public string EpicClaimId { get; set; }
        public string ExposureId { get; set; }
        public int PolicyPersonId { get; set; }
        public string ClaimNumber { get; set; }
        public int ExposureSequenceNumber { get; set; }
        public string AssignmentRecipientType { get; set; }
        public string AssignmentRecipientId { get; set; }
        public string AssignmentRecipientCCCId { get; set; }
        public string InspectionMethod { get; set; }
        public string InspectionType { get; set; }
        public string InstructionsToEstimator { get; set; }
        public string ClaimTypeCode { get; set; }
        public string AdjusterId { get; set; }
        public string AdjusterCCCId { get; set; }
        public string AdjusterFirstName { get; set; }
        public string AdjusterLastName { get; set; }
        public string AdjusterPhone { get; set; }
        public string AdjusterEmail { get; set; }
        public string AppraiserFirstName { get; set; }
        public string AppraiserLastName { get; set; }
        public string AppraiserPhone { get; set; }
        public string AppraiserType { get; set; }
        public string VINNumber { get; set; }
        public string LicensePlate { get; set; }
        public string LicenseState { get; set; }
        public string VehicleYear { get; set; }
        public string Make { get; set; }
        public string Model { get; set; }
        public int VehicleUsage { get; set; }
        public string Style { get; set; }
        public string Color { get; set; }
        public string VehicleType { get; set; }
        public string PriorDamages { get; set; }
        public string DamageDescription { get; set; }
        public bool? IsDrivable { get; set; }
        public string DamageExtent { get; set; }
        public string VehicleLocationType { get; set; }
        public string PhysicalLocationAddress1 { get; set; }
        public string PhysicalLocationAddressName { get; set; }
        public string PhysicalLocationCity { get; set; }
        public string PhysicalLocationCounty { get; set; }
        public string PhysicalLocationStateCode { get; set; }
        public string PhysicalLocationZipCode { get; set; }
        public string PhysicalLocationPhone { get; set; }
        public string IcaapLoggedInUser { get; set; }

        // Damages
        public bool Dmg1Front { get; set; }
        public bool Dmg1LeftFront { get; set; }
        public bool Dmg1RightFront { get; set; }
        public bool Dmg1Rear { get; set; }
        public bool Dmg1LeftRear { get; set; }
        public bool Dmg1RightRear { get; set; }
        public bool Dmg1LeftSide { get; set; }
        public bool Dmg1RightSide { get; set; }
        public bool Dmg1Undercarriage { get; set; }
        public bool Dmg1RoofTop { get; set; }
        public bool Dmg1Hood { get; set; }
        public bool Dmg1Mechanical { get; set; }
        public bool Dmg1Electrical { get; set; }
        public bool Dmg1Interior { get; set; }
        public bool Dmg1AwningOnly { get; set; }
        public bool Dmg1RollOver { get; set; }
        public bool Dmg2Front2 { get; set; }
        public bool Dmg2LeftFront { get; set; }
        public bool Dmg2RightFront { get; set; }
        public bool Dmg2Rear2 { get; set; }
        public bool Dmg2LeftRear { get; set; }
        public bool Dmg2RightRear { get; set; }
        public bool Dmg2LeftSide { get; set; }
        public bool Dmg2RightSide { get; set; }
        public bool Dmg2Undercarriage { get; set; }
        public bool Dmg2RoofTop { get; set; }
        public bool Dmg2Hood { get; set; }
        public bool Dmg2Mechanical { get; set; }
        public bool Dmg2Electrical { get; set; }
        public bool Dmg2Interior { get; set; }
        public bool Dmg2AwningOnly { get; set; }
        public bool Dmg2RollOver { get; set; }

        // Contacts
        public string CPRole { get; set; }
        public string PrimaryContactFirstName { get; set; }
        public string PrimaryContactLastName { get; set; }
        public string PrimaryContactPhone { get; set; }
        public string PrimaryVendorFullName { get; set; }
        public string VendorName { get; set; }
        public string ClaimantFirstName { get; set; }
        public string ClaimantLastName { get; set; }
        public string ClaimantContactType { get; set; }
        public string ClaimantCellPhone { get; set; }
        public string ClaimantHomePhone { get; set; }
        public string ClaimantBusinessPhone { get; set; }
        public string ClaimantEmail { get; set; }
        public string ClaimantAddress1 { get; set; }
        public string ClaimantAddressName { get; set; }
        public string ClaimantCity { get; set; }
        public string ClaimantCounty { get; set; }
        public string ClaimantStateCode { get; set; }
        public string ClaimantZipCode { get; set; }
        public string InjuryType { get; set; }

        // Loss
        public string LossDescription { get; set; }
        public DateTime LossDateTime { get; set; }
        public DateTime? LossReportDateTime { get; set; }
        public string LossLocationAddress1 { get; set; }
        public string LossLocationCity { get; set; }
        public string LossLocationCounty { get; set; }
        public string LossLocationStateCode { get; set; }
        public string LossLocationZipCode { get; set; }

        // Policy
        public string Deductible { get; set; }
        public string PolicyStateCode { get; set; }
        public DateTime? PolicyStartDate { get; set; }
        public DateTime? PolicyExpirationDate { get; set; }
        public string PolicySubType { get; set; }
        public string UnderwritingCompany { get; set; }
        public string AssignmentType { get; set; }
        public string ReserveCategory { get; set; }
        public string PolicyProduct { get; set; }

        //CCC Response
        public string Response { get; set; }
    }
}
