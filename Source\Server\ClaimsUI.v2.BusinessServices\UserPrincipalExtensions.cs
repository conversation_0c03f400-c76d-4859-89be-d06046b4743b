﻿using System;
using System.DirectoryServices;
using System.DirectoryServices.AccountManagement;
using System.Text.RegularExpressions;

namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices
{
    public static class UserPrincipalExtensions
    {
        /// <summary>
        /// Extracts email address from UserPrincipal, trying multiple sources
        /// Works for both service accounts and standard AD users
        /// </summary>
        /// <param name="userPrincipal">UserPrincipal instance</param>
        /// <param name="fallbackDomain">Optional domain to construct email if none found (e.g., "company.com")</param>
        /// <returns>Email address or null if none found</returns>
        public static string GetEmailAddress(this UserPrincipal userPrincipal, string fallbackDomain = null)
        {
            if (userPrincipal == null)
                return null;

            try
            {
                // 1. Try the EmailAddress property (maps to 'mail' attribute)
                if (!string.IsNullOrEmpty(userPrincipal.EmailAddress))
                    return userPrincipal.EmailAddress;

                // 2. Try UserPrincipalName if it looks like a valid email
                if (IsValidEmail(userPrincipal.UserPrincipalName))
                    return userPrincipal.UserPrincipalName;

                // 3. Try accessing underlying DirectoryEntry for more attributes
                string emailFromDirectoryEntry = GetEmailFromDirectoryEntry(userPrincipal);
                if (!string.IsNullOrEmpty(emailFromDirectoryEntry))
                    return emailFromDirectoryEntry;

                // 4. Fallback: Construct from sAMAccountName + fallbackDomain
                if (!string.IsNullOrEmpty(fallbackDomain) && !string.IsNullOrEmpty(userPrincipal.SamAccountName))
                    return $"{userPrincipal.SamAccountName}@{fallbackDomain}";

                // 5. Last resort: Return UPN even if not email format (for identification)
                return userPrincipal.UserPrincipalName;
            }
            catch (Exception)
            {
                // Log exception if needed, but don't throw
                return null;
            }
        }

        /// <summary>
        /// Gets email from DirectoryEntry underlying object, checking multiple attributes
        /// </summary>
        private static string GetEmailFromDirectoryEntry(UserPrincipal userPrincipal)
        {
            try
            {
                DirectoryEntry directoryEntry = (DirectoryEntry)userPrincipal.GetUnderlyingObject();

                // Try 'mail' attribute directly
                if (directoryEntry.Properties["mail"].Value != null)
                    return directoryEntry.Properties["mail"].Value.ToString();

                // Try 'proxyAddresses' for Exchange environments
                if (directoryEntry.Properties["proxyAddresses"].Value != null)
                {
                    var proxyAddresses = directoryEntry.Properties["proxyAddresses"];

                    // Look for primary SMTP address (starts with "SMTP:" in caps)
                    foreach (string address in proxyAddresses)
                    {
                        if (address.StartsWith("SMTP:", StringComparison.Ordinal))
                            return address.Substring(5);
                    }

                    // If no primary found, try any smtp address
                    foreach (string address in proxyAddresses)
                    {
                        if (address.StartsWith("smtp:", StringComparison.OrdinalIgnoreCase))
                            return address.Substring(5);
                    }
                }

                // Try 'internetEmailAddress' (some AD configurations use this)
                if (directoryEntry.Properties["internetEmailAddress"].Value != null)
                    return directoryEntry.Properties["internetEmailAddress"].Value.ToString();

            }
            catch (Exception)
            {
                // Ignore exceptions and return null
            }

            return null;
        }

        /// <summary>
        /// Validates if a string is a valid email format
        /// </summary>
        private static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                // Basic email regex pattern
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);

                // Exclude internal domains that aren't real emails
                if (email.EndsWith(".local", StringComparison.OrdinalIgnoreCase) ||
                    email.EndsWith(".internal", StringComparison.OrdinalIgnoreCase) ||
                    email.EndsWith(".corp", StringComparison.OrdinalIgnoreCase))
                    return false;

                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }
    }
}
