﻿.widget {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
    max-height: 350px;
    display: flex;
    flex-direction: column;
}

    .widget:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

/* Widget Header */
.widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right {
    display: flex;
    align-items: center;
}

.widget-icon {
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.contact-summary {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pending-badge {
    background: var(--secondary-bg);
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    padding: 2px 12px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.contacted-badge {
    background: var(--primary-bg);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: 2px 12px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Widget Content with Scrolling */
.widget-content {
    padding: 0;
    flex: 1; 
    display: flex;
    flex-direction: column;
    overflow: hidden; 
}

/* Contact Table - Scrollable */
.contact-table {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto; 
    overflow-x: hidden;
    padding: 16px;
}

    /* Custom scrollbar styling */
    .contact-table::-webkit-scrollbar {
        width: 6px;
    }

    .contact-table::-webkit-scrollbar-track {
        background: var(--bg-tertiary);
    }

    .contact-table::-webkit-scrollbar-thumb {
        background: var(--primary-bg);
        border-radius: 3px;
    }

        .contact-table::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color-3);
        }

.contact-row {
    display: flex;
    align-items: center;
    padding: 7px;
    border-bottom: 1px solid var(--border-light);
    transition: background-color 0.2s ease;
    gap: 12px;
}

    .contact-row:last-child {
        border-bottom: none;
    }

    .contact-row:hover {
        background: var(--primary-color-1);
    }

/* Contact Status - Now on the left */
.contact-status {
    flex-shrink: 0;
}

.contact-bullet {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

    .contact-bullet.pending {
        background: var(--secondary-bg);
        color: var(--secondary-color);
        border: 1px solid var(--secondary-color);
    }

    .contact-bullet.contacted {
        background: var(--primary-bg);
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
    }

.checkmark {
    font-size: 16px;
    font-weight: bold;
}

/* Contact Info - In the middle */
.contact-info {
    flex: 1;
    min-width: 0;
}

.contact-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1px; 
}

.contact-role {
    font-size: 12px;
    color: var(--text-muted);
}

/* Hi Marley Status - On the right */
.himarley-status {
    flex-shrink: 0;
    text-align: right;
    min-width: 100px;
}

.himarley-row {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
}

.himarley-title {
    font-size: 10px;
    font-weight: 500;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.himarley-text {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    font-weight: 500;
}

.status-icon {
    font-size: 12px;
}

/* Contact Footer */
.contact-footer {
    padding: 4px 8px;
    border-top: 1px solid var(--bg-secondary);
    flex-shrink: 0;
}

.contact-total {
    font-size: 14px;
    color: var(--text-muted);
    font-weight: 600;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
    text-align: center;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
}

/* Loading and No Data States */
.loading-indicator {
    text-align: center;
    color: rgba(167, 169, 169, 1);
    font-style: italic;
    padding: 2rem;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .header-right {
        align-self: flex-end;
    }

    .contact-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 12px 16px;
    }

    .contact-status {
        align-self: flex-end;
    }

    .contact-bullet {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .himarley-status {
        align-self: flex-start;
        text-align: left;
    }
}
