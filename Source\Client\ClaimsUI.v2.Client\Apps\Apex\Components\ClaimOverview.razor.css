﻿/* Claim Overview Styles */
.claim-overview-container {
    color: var(--text-primary);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    margin: 0;
    padding: 0 6rem;
    margin: 0 auto;
    margin-top: 24px;
    background: var(--bg-primary);
}

/* Overview Content */
.overview-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.overview-widgets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 24px;
}

/* Overview Loading Styles */
.overview-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 60px 20px;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    text-align: center;
}

/* Document with Spinner Ring */
.spinner-document {
    position: relative;
    width: 64px;
    height: 64px;
}

.spinner-ring {
    width: 64px;
    height: 64px;
    border: 2px solid var(--border-light);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
}

.document-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    animation: pulse 2s ease-in-out infinite;
}

.loading-text {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.loading-subtext {
    font-size: 14px;
    color: var(--text-muted);
    margin: 0;
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

/* Responsive Design */
@media (max-width: 1400px) {
    .overview-widgets {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 900px) {
    .overview-widgets {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .overview-loading {
        min-height: 300px;
        padding: 40px 20px;
    }

    .spinner-document {
        width: 56px;
        height: 56px;
    }

    .spinner-ring {
        width: 56px;
        height: 56px;
    }

    .document-center {
        font-size: 20px;
    }

    .loading-text {
        font-size: 16px;
    }
}