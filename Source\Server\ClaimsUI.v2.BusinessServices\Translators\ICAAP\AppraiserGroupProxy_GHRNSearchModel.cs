﻿using NGIC.Claims.Shared.Translator;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.Contact;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.ICAAP.MOIModels.GHRN;
using System;
using System.Collections.Generic;
using System.Linq;
using Proxy = NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Proxies.FunctionalCore;

namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Translators.ICAAP
{
    public class AppraiserGroupProxy_GHRNSearchModel : BaseTranslator<Proxy.AppraiserGroup, GHRNSearchModel>
    {
        protected override Proxy.AppraiserGroup DtoToEntity(Proxy.AppraiserGroup entity, GHRNSearchModel dto)
        {
            throw new NotImplementedException();
        }

        protected override GHRNSearchModel EntityToDto(GHRNSearchModel dto, Proxy.AppraiserGroup entity)
        {
            foreach (var appraiser in entity.Appraisers)
            {

                dto.GHRNShops.Add(new GHRNShopModel()
                {
                    AppraiserName = appraiser.OrganizationName,
                    AppraiserCode = appraiser.Code,
                    DistanceFromAddress = appraiser.DistanceFromAddress,
                    Address = GetAddress(appraiser.Addresses),
                    Phone = GetPhone(appraiser.PhoneNumbers)
                });
            }

            return dto;
        }

        private AddressModel GetAddress(List<Proxy.Address> addresses)
        {
            Proxy.Address address = addresses.FirstOrDefault();

            AddressModel addressModel = new AddressModel();

            if (address is not null) 
            {
                addressModel.AddressLine1 = address.AddressLine1;
                addressModel.AddressLine2 = address.AddressLine2;
                addressModel.City = address.City;
                addressModel.State = address.State;
                addressModel.ZipCode = address.ZipCode;
                addressModel.Country = address.Country;
            }

            return addressModel;
        }
        private ContactPhone GetPhone(List<Proxy.ContactPhone> phones)
        {
            Proxy.ContactPhone phone = phones.FirstOrDefault();

            ContactPhone contactPhone = new ();

            if (phone is not null)
            {
                contactPhone.PhoneNumber = phone.PhoneNumber;
                contactPhone.PhoneType = phone.PhoneType;
            }
            return contactPhone;
        }
    }
}
