<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>NGIC.Claims.Web.ClaimsUI.v2.Infrastructure</AssemblyName>
    <RootNamespace>NGIC.Claims.Web.ClaimsUI.v2.Infrastructure</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Dispatcher\Proxies\FunctionalCore\package-lock.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Common.Logger" Version="5.0.3" />
    <PackageReference Include="Common.Status" Version="3.0.0" />
    <PackageReference Include="DTO.Common" Version="3.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.16" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Shared.CredentialsManager" Version="2.0.2" />
    <PackageReference Include="Shared.Translator" Version="2.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\ClaimsUI.v2.BusinessModels\ClaimsUI.v2.BusinessModels.csproj" />
  </ItemGroup>

</Project>
