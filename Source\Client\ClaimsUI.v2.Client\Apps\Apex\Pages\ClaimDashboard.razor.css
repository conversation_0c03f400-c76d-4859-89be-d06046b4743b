﻿/* Claims Adjuster Dashboard Styles */
.dashboard-container {
    min-height: 100vh;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0 auto;
    padding: 0 40px;
}

.dashboard-header {
    background: var(--primary-color);
    padding: 1rem 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-lg);
    margin: 0 -40px;
}

.dashboard-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: white;
    padding: 0 6rem;
}

.user-info {
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 0 6rem;
}

.dashboard-nav {
    background: var(--bg-primary);
    padding: 0 6rem;
    position: relative;
}

    .dashboard-nav::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 6rem;
        right: 6rem;
        height: 1px;
        background: var(--border-light);
    }

.nav-item {
    display: inline-flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--text-secondary);
    background: transparent;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    gap: 8px;
}

    .nav-item.active {
        color: var(--primary-color);
        background: var(--bg-secondary);
        border-bottom: 2px solid var(--primary-color);
    }

    .nav-item:hover {
        color: var(--primary-color-8);
        border-bottom: 2px solid var(--primary-color-8);
    }

    /* Blue color scheme for My Claims tabs */
    .nav-item.my-claim-tab.active {
        color: var(--primary-color);
        background: var(--bg-secondary);
        border-bottom: 2px solid var(--primary-color);
    }

    .nav-item.my-claim-tab:hover {
        color: var(--primary-color-8);
        border-bottom: 2px solid var(--primary-color-8);
    }

    /* Orange color scheme for Search Claims tabs */
    .nav-item.search-claim-tab.active {
        color: var(--secondary-color);
        background: var(--bg-secondary);
        border-bottom: 2px solid var(--secondary-color);
    }

    .nav-item.search-claim-tab:hover {
        color: var(--secondary-color-8);
        border-bottom: 2px solid var(--secondary-color-8);
    }

.close-tab-btn {
    background: transparent;
    border: none;
    color: inherit;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    line-height: 1;
}

    .close-tab-btn:hover {
        background: var(--bg-secondary);
        color: var(--primary-color-8);
    }

/* Two Column Layout */
.dashboard-content {
    display: grid;
    grid-template-columns: 0.8fr 0.8fr;
    gap: 2rem;
    padding: 2rem 6rem;
    height: calc(100vh - 200px);
    justify-content: center;
}

.left-column, .right-column {
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    box-shadow: 0 4px 20px var(--shadow);
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
}

    .left-column:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

    .right-column:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--secondary-color);
    }

.section-header {
    padding: 1rem 1.5rem;
}

.left-column .section-header {
    background: linear-gradient(135deg, var(--primary-color-8), var(--primary-color-6));
    box-shadow: 0 2px 8px var(--primary-bg);
    border-bottom: 2px solid var(--primary-color-8);
}

.right-column .section-header {
    background: linear-gradient(135deg, var(--secondary-color-8), var(--secondary-color-6));
    box-shadow: 0 2px 8px var(--secondary-bg);
    border-bottom: 2px solid var(--secondary-color-8);
}

.section-title {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-icon {
    font-size: 1.1rem;
}

.filter-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--primary-color-8);
    background: linear-gradient(135deg, var(--primary-bg2), var(--primary-bg3));
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-input {
    flex: 1;
    max-width: 400px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid var(--primary-color-8);
    border-radius: 8px;
    transition: all 0.3s ease;
}

    .filter-input::placeholder {
        color: var(--text-primary);
    }

    .filter-input:focus {
        outline: 1px solid var(--primary-color);
        background: var(--primary-color-1);
        border-color: var(--primary-color);
    }

.refresh-filter-btn {
    background: var(--primary-color-8);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px var(--primary-color-3);
    flex-shrink: 0;
}

    .refresh-filter-btn:hover {
        background: var(--primary-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px var(--primary-color-4);
    }

.clear-filter-btn {
    background: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

    .clear-filter-btn:hover {
        background: var(--primary-color-1);
        border-color: var(--primary-color);
    }

.search-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--secondary-color-8);
    background: linear-gradient(135deg, var(--secondary-bg2), var(--secondary-bg3));
}

.search-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    max-width: 300px;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid var(--secondary-color-8);
    border-radius: 8px;
    transition: all 0.3s ease;
}

    .search-input::placeholder {
        color: var(--text-primary);
    }

    .search-input:focus {
        outline: 1px solid var(--secondary-color);
        background: var(--secondary-color-1);
        border-color: var(--secondary-color);
    }

.search-button {
    background: var(--secondary-color-8);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px var(--secondary-color-3);
    flex-shrink: 0;
}

    .search-button:hover {
        background: var(--secondary-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px var(--secondary-color-4);
    }

.clear-search-btn {
    background: transparent;
    border: 1px solid var(--secondary-color);
    color: var(--secondary-color);
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

    .clear-search-btn:hover {
        background: var(--secondary-color-1);
        border-color: var(--secondary-color);
    }

.empty-search-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    color: var(--text-muted);
    text-align: center;
}

    .empty-search-state .empty-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        /*opacity: 0.5;*/
    }

    .empty-search-state .empty-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .empty-search-state .empty-description {
        font-size: 0.85rem;
        max-width: 280px;
        line-height: 1.4;
    }

.table-container {
    flex: 1;
    overflow: auto;
    background: rgba(255, 255, 255, 0.02);
}

.claims-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

    .claims-table th {
        background: var(--bg-tertiary);
        padding: 16px;
        text-align: left;
        font-weight: 550;
        color: var(--text-primary);
        border-bottom: 2px solid var(--border-light);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .claims-table td {
        padding: 16px;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border-light);
    }

.claim-row {
    transition: background-color 0.2s ease;
    cursor: pointer;
}

    .claim-row:hover {
        background: var(--primary-color-1);
    }

.claim-number-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Loading States */
.loading-cell {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.02);
}

.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    text-align: center;
    font-style: italic;
}

.no-results {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem;
}

/* Claim Header */
.claim-header {
    background: var(--bg-secondary);
    margin: 1.5rem 6rem;
    padding: 1rem 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow);
    position: relative;
}

.claim-header-main {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.claim-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    letter-spacing: -0.5px;
    text-shadow: 1px 1px 2px var(--shadow-light);
}

.claim-details {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.claim-detail-item {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.detail-label {
    font-size: 0.9rem;
    font-weight: 600;
}

.detail-value {
    font-size: 0.9rem;
    font-weight: 500;
    text-shadow: 1px 1px 2px var(--shadow-light);
}

.detail-separator {
    font-weight: bold;
    font-size: 0.8rem;
}

/* My Claims */
.claim-header.my-claim-header .claim-number,
.claim-header.my-claim-header .detail-label {
    color: var(--primary-color);
}

.claim-header.my-claim-header .detail-value {
    color: var(--primary-color);
    font-weight: bold;
}

.claim-header.my-claim-header .detail-separator {
    color: var(--primary-color);
}

/* Search Claims - Orange colors */
.claim-header.search-claim-header .claim-number,
.claim-header.search-claim-header .detail-label {
    color: var(--secondary-color);
}

.claim-header.search-claim-header .detail-value {
    color: var(--secondary-color);
    font-weight: bold;
}

.claim-header.search-claim-header .detail-separator {
    color: var(--secondary-color);
}

/* Responsive design for claim header */
@media (max-width: 768px) {
    .claim-header {
        margin: 1rem;
        padding: 1rem 1.5rem;
    }

    .claim-number {
        font-size: 1.25rem;
    }

    .claim-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .detail-separator {
        display: none;
    }
}

/* Status Badge Styles */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-closed {
    background: var(--primary-bg);
    color: var(--primary-color);
}

.status-open {
    background: var(--secondary-bg);
    color: var(--secondary-color);
}

.status-pending {
    background: rgba(167,169,169,0.15);
    color: rgba(167,169,169,1);
}

.status-under-investigation {
    background: rgba(50,184,198,1,0.15);
    color: rgba(50,184,198,1);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        height: auto;
    }

    .left-column, .right-column {
        min-height: 400px;
    }

    .claims-table {
        font-size: 0.8rem;
    }

        .claims-table th, .claims-table td {
            padding: 8px;
        }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .search-input-group {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .search-button, .clear-search-btn {
        width: 100%;
    }

    .filter-section {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .clear-filter-btn {
        width: 100%;
    }

    .claims-table {
        min-width: 600px;
    }

    .table-container {
        overflow-x: auto;
    }
}