﻿using NGIC.Claims.Common.Status.Response.Contracts;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Contracts;
using NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Contracts;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Proxy = NGIC.Claims.Web.ClaimsUI.v2.Infrastructure.Dispatcher.Proxies.FunctionalCore;


namespace NGIC.Claims.Web.ClaimsUI.v2.BusinessServices.Impl
{
    public class ExposureService : BaseService, IExposureService
    {
        public ExposureService(
            IDispatcher dispatcher,
            ITranslatorService translator,
            IStatusResponse statusResponse
        ) : base(translator, dispatcher, statusResponse)
        {
        }
        public async Task<List<Exposure>> GetExposuresByClaimNumber(string claimNumber)
        {
            List<Exposure> exposures = new();

            IEnumerable<Proxy.Exposure> apexExposures = await Dispatcher.DispatchRequest<IEnumerable<Proxy.Exposure>, Proxy.IFunctionalCoreProxy>(
                                                    x => x.Exposure_GetApexExposuresByClaimNumberAsync(claimNumber));

            return apexExposures != null && apexExposures.Any()
                ? Translator.TranslateRangeDefault<Exposure>(apexExposures).ToList()
                : exposures;
        }
    }
}