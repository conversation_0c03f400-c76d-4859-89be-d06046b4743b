﻿.widget {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-light);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 250ms cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
    max-height: 350px;
    display: flex;
    flex-direction: column;
}

    .widget:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--primary-color);
    }

/* Widget Header */
.widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
    flex-shrink: 0;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right {
    display: flex;
    align-items: center;
}

.widget-icon {
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.liability-summary {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pending-badge {
    background: var(--secondary-bg);
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    padding: 2px 12px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.determined-badge {
    background: var(--primary-bg);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: 2px 12px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Widget Content */
.widget-content {
    padding: 8px 16px;
    flex: 1;
    overflow-y: auto;
}

    /* Custom scrollbar styling */
    .widget-content::-webkit-scrollbar {
        width: 6px;
    }

    .widget-content::-webkit-scrollbar-track {
        background: var(--bg-tertiary);
    }

    .widget-content::-webkit-scrollbar-thumb {
        background: var(--primary-bg);
        border-radius: 3px;
    }

        .widget-content::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color-3);
        }

/* Liability Content */
.liability-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    height: 100%;
}

.liability-section {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-light);
}

.section-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    min-height: 20px;
}

.section-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
    min-width: 100px;
}

.section-value {
    font-size: 14px;
    font-weight: 550;
    color: var(--text-primary);
    text-align: right;
    flex: 1;
}

.police-report {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 6px;
}

.status-icon {
    font-size: 12px;
}

/* SIU Red Flags Section */
.siu-section {
    background: var(--secondary-bg);
    border: 1px solid var(--secondary-color);
    border-radius: 8px;
    padding: 10px;
    margin-top: auto;
    margin-bottom: auto;
    align-self: stretch;
}

.siu-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--secondary-color);
}

.siu-icon {
    font-size: 18px;
    color: var(--secondary-color);
}

.siu-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.siu-flags {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.siu-flag {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 14px;
}

.flag-bullet {
    color: var(--secondary-color);
    font-weight: bold;
    flex-shrink: 0;
    margin-top: 1px;
}

.flag-text {
    color: var(--secondary-color);
    font-weight: 500;
}

/* Loading and No Data States */
.loading-indicator {
    text-align: center;
    color: rgba(167, 169, 169, 1);
    font-style: italic;
    padding: 2rem;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: rgba(167, 169, 169, 0.7);
    text-align: center;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .widget-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .header-right {
        align-self: flex-end;
    }

    .section-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .section-value {
        text-align: left;
        width: 100%;
    }

    .police-report {
        justify-content: flex-start;
    }

    .siu-flag {
        font-size: 11px;
    }
}
