﻿using Microsoft.AspNetCore.Components;
using NGIC.Claims.Shared.Translator.Interfaces;
using NGIC.Claims.Web.ClaimsUI.v2.BusinessModels.Common;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.ViewModels;
using NGIC.Claims.Web.ClaimsUI.v2.Client.Shared.HttpHelpers;

namespace NGIC.Claims.Web.ClaimsUI.v2.Client.Apps.Apex.Components
{
    public partial class ContactStatus : ComponentBase
    {
        [Inject] private IHttpDispatcher HttpDispatcher { get; set; }
        [Inject] private ITranslatorService Translator { get; set; }
        
        [Parameter] public ClaimSummaryModel ClaimSummary { get; set; }

        private List<ContactStatusModel> contacts => ClaimSummary?.Contacts;
        private bool isLoading = false;
        
        protected override async Task OnInitializedAsync()
        {
            if (!ClaimSummary.IsWidgetLoaded("ContactStatus"))
            {
                await LoadContacts();
            }
        }

        private async Task LoadContacts()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                // Translate ExposureModel to List<ContactStatusModel> and Order by ExposureNumber
                ClaimSummary.Contacts = Translator.TranslateRange<ContactStatusModel>(ClaimSummary.Exposures)
                    .OrderBy(c => int.TryParse(c.ExposureNumber, out int exposureNum) ? exposureNum : int.MaxValue)
                    .ToList();

                // TODO: Get rid of this if statement
                if (!ClaimSummary.Contacts.Any())
                {
                    await Task.Delay(300);
                    ClaimSummary.Contacts = GenerateContacts();
                }
                else
                {
                    foreach(ContactStatusModel contact in ClaimSummary.Contacts)
                    {
                        contact.HiMarleyStatus = GenerateHiMarleyStatus();
                    }
                }
                ClaimSummary.MarkWidgetAsLoaded("ContactStatus");
            }
            catch (Exception ex)
            {
                // TODO: Add proper logging
                Console.WriteLine($"Error loading contacts: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private async Task<IEnumerable<Exposure>> GetEpicExposuresByClaimNumber(string claimNumber)
        {
            return await HttpDispatcher.Get<List<Exposure>>($"{Routes.Exposure.GetExposuresByClaimNumber}{claimNumber}");
        }

        private async Task RefreshContacts()
        {
            await LoadContacts();
        }

        // TODO: Remove everything below once we have the data
        // ***********************************************************
        private static List<ContactStatusModel> GenerateContacts()
        {
            List<ContactStatusModel> contacts = new();
            for (int i = 0; i < 4; i++)
            {
                contacts.Add(new ContactStatusModel
                {
                    ExposureNumber = (i+1).ToString(),
                    HiMarleyStatus = GenerateHiMarleyStatus(),
                    Name = GenerateName(),
                    Role = GenerateRole(),
                    Status = GenerateStatus()
                });
            }
            return contacts;
        }

        private static string GenerateHiMarleyStatus()
        {
            Random random = new Random();
            string[] statuses = { "Opted In", "Not Opted In", "Pending" };
            return statuses[random.Next(statuses.Length)];
        }

        private static string GenerateName()
        {
            Random random = new Random();

            string firstName = FirstNames[random.Next(FirstNames.Length)];
            string lastName = LastNames[random.Next(LastNames.Length)];

            return $"{firstName} {lastName}";
        }

        private static readonly string[] FirstNames = {
            "James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda",
            "William", "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica",
            "Thomas", "Sarah", "Christopher", "Karen", "Charles", "Nancy", "Daniel", "Lisa",
            "Matthew", "Betty", "Anthony", "Helen", "Mark", "Sandra", "Donald", "Donna",
            "Steven", "Carol", "Paul", "Ruth", "Andrew", "Sharon", "Joshua", "Michelle",
            "Kenneth", "Laura", "Kevin", "Sarah", "Brian", "Kimberly", "George", "Deborah",
            "Timothy", "Dorothy", "Ronald", "Amy", "Jason", "Angela", "Edward", "Ashley",
            "Jeffrey", "Brenda", "Ryan", "Emma", "Jacob", "Olivia", "Gary", "Cynthia"
        };

        private static readonly string[] LastNames = {
            "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
            "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas",
            "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White",
            "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker", "Young",
            "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
            "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell",
            "Carter", "Roberts", "Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker",
            "Cruz", "Edwards", "Collins", "Reyes", "Stewart", "Morris", "Morales", "Murphy"
        };

        private static string GenerateRole()
        {
            Random random = new Random();
            string[] roles = { "Driver", "Driver & Owner", "Excluded Driver", "Other", "Passenger", "Pedestrian", "Undeclared Driver", "Unlisted Driver" };
            return roles[random.Next(roles.Length)];
        }
        
        private static string GenerateStatus()
        {
            Random random = new Random();
            string[] statuses = { "Contacted", "Pending" };
            return statuses[random.Next(statuses.Length)];
        }
    }
}